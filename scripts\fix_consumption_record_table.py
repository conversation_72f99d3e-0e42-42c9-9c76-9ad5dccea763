#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
修复 consumption_record 表结构
添加缺失的 user_agent 字段
"""

import sys
import os
import sqlite3
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def check_table_structure():
    """检查当前表结构"""
    print("🔍 检查 consumption_record 表结构...")
    
    try:
        conn = sqlite3.connect('dianka.db')
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='consumption_record'
        """)
        
        if not cursor.fetchone():
            print("   ❌ consumption_record 表不存在")
            conn.close()
            return False
        
        # 检查表结构
        cursor.execute("PRAGMA table_info(consumption_record)")
        columns = cursor.fetchall()
        
        print("   📋 当前表结构:")
        column_names = []
        for col in columns:
            column_names.append(col[1])
            print(f"      - {col[1]}: {col[2]} (NOT NULL: {col[3]})")
        
        # 检查是否有 user_agent 字段
        has_user_agent = 'user_agent' in column_names
        print(f"   📋 是否有 user_agent 字段: {has_user_agent}")
        
        conn.close()
        return has_user_agent
        
    except Exception as e:
        print(f"   ❌ 检查表结构失败: {e}")
        return False

def add_user_agent_column():
    """添加 user_agent 字段"""
    print("🔧 添加 user_agent 字段...")
    
    try:
        conn = sqlite3.connect('dianka.db')
        cursor = conn.cursor()
        
        # 添加 user_agent 字段
        cursor.execute("ALTER TABLE consumption_record ADD COLUMN user_agent TEXT")
        
        conn.commit()
        conn.close()
        
        print("   ✅ user_agent 字段添加成功")
        return True
        
    except Exception as e:
        print(f"   ❌ 添加 user_agent 字段失败: {e}")
        return False

def verify_fix():
    """验证修复结果"""
    print("🔍 验证修复结果...")
    
    try:
        conn = sqlite3.connect('dianka.db')
        cursor = conn.cursor()
        
        # 检查表结构
        cursor.execute("PRAGMA table_info(consumption_record)")
        columns = cursor.fetchall()
        
        column_names = [col[1] for col in columns]
        
        if 'user_agent' in column_names:
            print("   ✅ user_agent 字段存在")
            
            # 测试插入数据
            try:
                test_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                cursor.execute("""
                    INSERT INTO consumption_record 
                    (user_id, software_id, points_consumed, ip_address, user_agent, timestamp)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (1, 1, 1, '127.0.0.1', 'Test User Agent', test_time))
                
                record_id = cursor.lastrowid
                
                # 删除测试记录
                cursor.execute("DELETE FROM consumption_record WHERE id = ?", (record_id,))
                
                conn.commit()
                print("   ✅ 插入测试成功")
                
            except Exception as e:
                print(f"   ❌ 插入测试失败: {e}")
                conn.rollback()
                conn.close()
                return False
            
            conn.close()
            return True
        else:
            print("   ❌ user_agent 字段不存在")
            conn.close()
            return False
            
    except Exception as e:
        print(f"   ❌ 验证失败: {e}")
        return False

def test_deduct_points_function():
    """测试扣费功能"""
    print("🔍 测试扣费功能...")
    
    try:
        from bypass_sqlalchemy import DatabaseManager
        
        db_manager = DatabaseManager()
        
        # 获取测试用户
        admin_user = db_manager.get_user_by_username('admin')
        if not admin_user:
            print("   ❌ 未找到管理员用户")
            return False
        
        print(f"   📋 扣费前用户点数: {admin_user['points']}")
        
        # 获取软件信息
        with db_manager.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM software WHERE is_active = 1 LIMIT 1")
            software_row = cursor.fetchone()
            
            if not software_row:
                print("   ❌ 未找到激活的软件")
                return False
            
            software = dict(software_row)
            print(f"   📋 使用软件: {software['name']} (ID: {software['id']})")
        
        # 尝试扣费
        try:
            record_id = db_manager.deduct_user_points(
                user_id=admin_user['id'],
                points=1,
                software_id=software['id'],
                ip_address='127.0.0.1',
                user_agent='Fix Script Test'
            )
            
            print(f"   ✅ 扣费成功，记录ID: {record_id}")
            
            # 检查扣费后的点数
            updated_user = db_manager.get_user_by_id(admin_user['id'])
            print(f"   📋 扣费后用户点数: {updated_user['points']}")
            
            return True
            
        except Exception as e:
            print(f"   ❌ 扣费失败: {e}")
            import traceback
            traceback.print_exc()
            return False
            
    except Exception as e:
        print(f"   ❌ 测试扣费功能失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始修复 consumption_record 表结构")
    print("=" * 60)
    
    # 1. 检查当前表结构
    has_user_agent = check_table_structure()
    
    if has_user_agent:
        print("✅ user_agent 字段已存在，无需修复")
    else:
        # 2. 添加 user_agent 字段
        if not add_user_agent_column():
            print("❌ 添加字段失败")
            return False
        
        # 3. 验证修复结果
        if not verify_fix():
            print("❌ 验证修复失败")
            return False
    
    # 4. 测试扣费功能
    if not test_deduct_points_function():
        print("❌ 扣费功能测试失败")
        return False
    
    print("\n✅ consumption_record 表修复完成！")
    print("🎉 扣费功能现在应该可以正常工作了")
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n🎯 修复成功！")
            sys.exit(0)
        else:
            print("\n❌ 修复失败！")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⚠️  修复被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 修复过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
