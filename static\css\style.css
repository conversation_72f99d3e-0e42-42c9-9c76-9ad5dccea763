/* 基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f4f4f4;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 导航栏 */
.navbar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 0;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.navbar .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.navbar h1 {
    font-size: 1.8rem;
    font-weight: 700;
    background: linear-gradient(45deg, #fff, #f8f9fa);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.navbar nav a {
    color: white;
    text-decoration: none;
    margin-left: 15px;
    padding: 10px 18px;
    border-radius: 25px;
    transition: all 0.3s ease;
    font-weight: 500;
    position: relative;
    overflow: hidden;
}

.navbar nav a:hover {
    background: rgba(255,255,255,0.2);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.navbar nav a.admin-link {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    color: white;
}

.navbar nav a.user-link {
    background: linear-gradient(45deg, #4834d4, #686de0);
    color: white;
}

.navbar nav a.danger-link {
    background: linear-gradient(45deg, #eb4d4b, #c0392b);
    color: white;
}

/* 主要内容区域 */
.main-content {
    padding: 2rem 0;
    min-height: calc(100vh - 120px);
}

/* 卡片样式 */
.card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    padding: 2rem;
    margin-bottom: 2rem;
}

.card h2 {
    margin-bottom: 1rem;
    color: #2c3e50;
    border-bottom: 2px solid #3498db;
    padding-bottom: 0.5rem;
}

/* 表单样式 */
.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #555;
}

.form-control {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
    transition: border-color 0.3s;
}

.form-control:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

/* 按钮样式 */
.btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    text-decoration: none;
    font-size: 1rem;
    transition: all 0.3s;
    text-align: center;
}

.btn-primary {
    background-color: #3498db;
    color: white;
}

.btn-primary:hover {
    background-color: #2980b9;
}

.btn-success {
    background-color: #27ae60;
    color: white;
}

.btn-success:hover {
    background-color: #229954;
}

.btn-danger {
    background-color: #e74c3c;
    color: white;
}

.btn-danger:hover {
    background-color: #c0392b;
}

.btn-warning {
    background-color: #f39c12;
    color: white;
}

.btn-warning:hover {
    background-color: #e67e22;
}

.btn-secondary {
    background-color: #95a5a6;
    color: white;
}

.btn-secondary:hover {
    background-color: #7f8c8d;
}

.btn-info {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
}

.btn-info:hover {
    background: linear-gradient(135deg, #5a67d8, #6b46c1);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

/* 现代化按钮效果 */
.btn {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a67d8, #6b46c1);
}

.btn-success {
    background: linear-gradient(135deg, #56ab2f, #a8e6cf);
    border: none;
}

.btn-success:hover {
    background: linear-gradient(135deg, #4a9b2e, #96d9c4);
}

.btn-warning {
    background: linear-gradient(135deg, #f093fb, #f5576c);
    border: none;
}

.btn-warning:hover {
    background: linear-gradient(135deg, #e081f5, #f04759);
}

.btn-danger {
    background: linear-gradient(135deg, #eb4d4b, #c0392b);
    border: none;
}

.btn-danger:hover {
    background: linear-gradient(135deg, #e74c3c, #a93226);
}

/* 消息提示 */
.flash-messages {
    margin-bottom: 1rem;
}

.alert {
    padding: 1rem;
    border-radius: 4px;
    margin-bottom: 1rem;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.alert-error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.alert-info {
    background-color: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

/* 表格样式 */
.table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
}

.table th,
.table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #555;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

/* 统计卡片 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    text-align: center;
}

.stat-card h3 {
    font-size: 2rem;
    color: #3498db;
    margin-bottom: 0.5rem;
}

.stat-card p {
    color: #666;
    font-size: 0.9rem;
}

/* 分页 */
.pagination {
    display: flex;
    justify-content: center;
    margin-top: 2rem;
}

.pagination a,
.pagination span {
    padding: 0.5rem 1rem;
    margin: 0 0.25rem;
    border: 1px solid #ddd;
    color: #3498db;
    text-decoration: none;
    border-radius: 4px;
}

.pagination a:hover {
    background-color: #3498db;
    color: white;
}

.pagination .current {
    background-color: #3498db;
    color: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 0 10px;
    }
    
    .navbar .container {
        flex-direction: column;
        gap: 1rem;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .table {
        font-size: 0.9rem;
    }
    
    .card {
        padding: 1rem;
    }
}
