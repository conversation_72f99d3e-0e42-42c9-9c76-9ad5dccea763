#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
修复软件表结构问题
添加缺失的 description 和 created_by 字段
"""

import sys
import os
import sqlite3
from datetime import datetime
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def check_column_exists(cursor, table_name, column_name):
    """检查列是否存在"""
    cursor.execute(f"PRAGMA table_info({table_name})")
    columns = [column[1] for column in cursor.fetchall()]
    return column_name in columns

def fix_software_table():
    """修复软件表结构"""
    print("🔧 修复软件表结构...")
    
    db_path = "point_card_platform.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查当前表结构
        cursor.execute("PRAGMA table_info(software)")
        columns = [column[1] for column in cursor.fetchall()]
        print(f"   当前软件表列: {columns}")
        
        # 检查并添加 description 字段
        if not check_column_exists(cursor, 'software', 'description'):
            print("   添加 description 列...")
            cursor.execute("ALTER TABLE software ADD COLUMN description TEXT")
            print("   ✅ description 列添加成功")
        else:
            print("   ✅ description 列已存在")
        
        # 检查并添加 created_by 字段
        if not check_column_exists(cursor, 'software', 'created_by'):
            print("   添加 created_by 列...")
            cursor.execute("ALTER TABLE software ADD COLUMN created_by INTEGER")
            print("   ✅ created_by 列添加成功")
        else:
            print("   ✅ created_by 列已存在")
        
        conn.commit()
        conn.close()
        
        print("   ✅ 软件表结构修复完成")
        return True
        
    except Exception as e:
        print(f"   ❌ 修复软件表失败: {e}")
        return False

def update_existing_software():
    """更新现有软件数据"""
    print("📝 更新现有软件数据...")
    
    db_path = "point_card_platform.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 为没有描述的软件添加默认描述
        cursor.execute("""
            UPDATE software 
            SET description = '暂无描述' 
            WHERE description IS NULL OR description = ''
        """)
        updated_desc = cursor.rowcount
        
        # 为没有创建者的软件设置默认创建者（假设是第一个管理员）
        cursor.execute("SELECT id FROM user WHERE is_admin = 1 ORDER BY id LIMIT 1")
        admin_result = cursor.fetchone()
        admin_id = admin_result[0] if admin_result else 1
        
        cursor.execute("""
            UPDATE software 
            SET created_by = ? 
            WHERE created_by IS NULL
        """, (admin_id,))
        updated_creator = cursor.rowcount
        
        conn.commit()
        conn.close()
        
        print(f"   ✅ 更新了 {updated_desc} 个软件的描述")
        print(f"   ✅ 更新了 {updated_creator} 个软件的创建者")
        return True
        
    except Exception as e:
        print(f"   ❌ 更新软件数据失败: {e}")
        return False

def verify_software_table():
    """验证软件表结构"""
    print("🔍 验证软件表结构...")
    
    db_path = "point_card_platform.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查表结构
        cursor.execute("PRAGMA table_info(software)")
        columns = [column[1] for column in cursor.fetchall()]
        
        required_columns = ['id', 'name', 'description', 'api_key', 'points_per_use', 'is_active', 'created_by', 'created_at']
        missing_columns = [col for col in required_columns if col not in columns]
        
        if missing_columns:
            print(f"   ❌ 软件表缺少列: {missing_columns}")
            return False
        else:
            print("   ✅ 软件表结构正确")
        
        # 检查数据
        cursor.execute("SELECT COUNT(*) FROM software")
        software_count = cursor.fetchone()[0]
        print(f"   📊 当前软件数量: {software_count}")
        
        if software_count > 0:
            cursor.execute("""
                SELECT name, description, points_per_use, is_active, created_by 
                FROM software 
                LIMIT 3
            """)
            software_list = cursor.fetchall()
            
            print("   📋 软件示例:")
            for software in software_list:
                print(f"     - {software[0]}: {software[1][:30]}{'...' if len(software[1]) > 30 else ''}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"   ❌ 验证软件表失败: {e}")
        return False

def test_create_software():
    """测试创建软件功能"""
    print("🧪 测试创建软件功能...")
    
    try:
        from bypass_sqlalchemy import DatabaseManager
        db_manager = DatabaseManager()
        
        # 测试创建软件
        test_name = f"测试软件_{datetime.now().strftime('%H%M%S')}"
        test_description = "这是一个测试软件，用于验证软件创建功能"
        test_points = 5
        test_api_key = f"test_key_{datetime.now().strftime('%H%M%S')}"
        test_creator = 1
        
        software_id = db_manager.create_software(
            name=test_name,
            description=test_description,
            points_per_use=test_points,
            api_key=test_api_key,
            created_by=test_creator
        )
        
        if software_id:
            print(f"   ✅ 测试软件创建成功，ID: {software_id}")
            print(f"     名称: {test_name}")
            print(f"     描述: {test_description}")
            print(f"     扣费点数: {test_points}")
            
            # 删除测试软件
            with db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("DELETE FROM software WHERE id = ?", (software_id,))
                conn.commit()
            print("   🗑️  测试软件已清理")
            
            return True
        else:
            print("   ❌ 测试软件创建失败")
            return False
        
    except Exception as e:
        print(f"   ❌ 测试创建软件失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 开始修复软件表结构问题")
    print("=" * 60)
    
    # 1. 修复软件表结构
    if not fix_software_table():
        print("❌ 软件表结构修复失败")
        return False
    
    # 2. 更新现有软件数据
    if not update_existing_software():
        print("❌ 软件数据更新失败")
        return False
    
    # 3. 验证表结构
    if not verify_software_table():
        print("❌ 软件表验证失败")
        return False
    
    # 4. 测试创建功能
    if not test_create_software():
        print("❌ 软件创建功能测试失败")
        return False
    
    print("\n🎉 软件表结构修复完成！")
    print("现在可以正常创建软件了。")
    return True

if __name__ == "__main__":
    if main():
        print("\n✅ 修复成功！现在可以正常添加软件了。")
    else:
        print("\n❌ 修复失败！请检查错误信息。")
        sys.exit(1)
