{% extends "base.html" %}

{% block title %}管理员管理 - 超级管理员{% endblock %}

{% block content %}
<style>
.super-admin-header {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    color: white;
    padding: 2rem;
    border-radius: 15px;
    margin-bottom: 2rem;
    text-align: center;
}

.admins-table {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.table-header {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    padding: 1rem;
}

.admin-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    margin-right: 1rem;
}

.admin-info {
    display: flex;
    align-items: center;
}

.admin-badge {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.8rem;
}

.super-admin-badge {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.8rem;
}

.status-active {
    color: #27ae60;
    font-weight: 600;
}

.status-inactive {
    color: #e74c3c;
    font-weight: 600;
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.btn-sm {
    padding: 4px 12px;
    font-size: 0.8rem;
}
</style>

<div class="super-admin-header">
    <h1>👥 管理员管理</h1>
    <p>管理系统中的所有管理员账户</p>
</div>

<div style="margin-bottom: 2rem; text-align: center;">
    <a href="{{ url_for('admin.create_admin') }}" class="btn btn-primary">创建新管理员</a>
</div>

{% if admins.items %}
<div class="admins-table">
    <div class="table-header">
        <h3 style="margin: 0;">管理员列表</h3>
    </div>
    
    <table class="table" style="margin: 0;">
        <thead style="background: #f8f9fa;">
            <tr>
                <th style="padding: 1rem;">管理员信息</th>
                <th style="padding: 1rem;">邮箱</th>
                <th style="padding: 1rem;">权限级别</th>
                <th style="padding: 1rem;">状态</th>
                <th style="padding: 1rem;">创建时间</th>
                <th style="padding: 1rem;">最后登录</th>
                <th style="padding: 1rem;">操作</th>
            </tr>
        </thead>
        <tbody>
            {% for admin in admins.items %}
            <tr style="border-bottom: 1px solid #eee;">
                <td style="padding: 1rem;">
                    <div class="admin-info">
                        <div class="admin-avatar">
                            {{ admin.username[0].upper() }}
                        </div>
                        <div>
                            <strong style="color: #2c3e50;">{{ admin.username }}</strong>
                            {% if admin.id == current_user.id %}
                            <span style="color: #3498db; font-size: 0.8rem;">(当前用户)</span>
                            {% endif %}
                        </div>
                    </div>
                </td>
                <td style="padding: 1rem;">
                    <span style="color: #666;">{{ admin.email }}</span>
                </td>
                <td style="padding: 1rem;">
                    {% if admin.is_super_admin %}
                        <span class="super-admin-badge">超级管理员</span>
                    {% else %}
                        <span class="admin-badge">普通管理员</span>
                    {% endif %}
                </td>
                <td style="padding: 1rem;">
                    {% if admin.is_admin %}
                        <span class="status-active">✓ 启用</span>
                    {% else %}
                        <span class="status-inactive">✗ 禁用</span>
                    {% endif %}
                </td>
                <td style="padding: 1rem;">
                    <small style="color: #666;">{{ admin.created_at.strftime('%Y-%m-%d') }}</small>
                </td>
                <td style="padding: 1rem;">
                    {% if admin.last_login %}
                        <small style="color: #666;">{{ admin.last_login.strftime('%Y-%m-%d %H:%M') }}</small>
                    {% else %}
                        <small style="color: #999;">从未登录</small>
                    {% endif %}
                </td>
                <td style="padding: 1rem;">
                    {% if admin.id != current_user.id %}
                    <div class="action-buttons">
                        <a href="{{ url_for('admin.toggle_admin_status', user_id=admin.id) }}" 
                           class="btn btn-sm {{ 'btn-warning' if admin.is_admin else 'btn-success' }}"
                           onclick="return confirm('确定要{{ '禁用' if admin.is_admin else '启用' }}此管理员吗？')">
                            {{ '禁用' if admin.is_admin else '启用' }}
                        </a>
                        
                        {% if admin.is_admin %}
                        <a href="{{ url_for('admin.toggle_super_admin_status', user_id=admin.id) }}" 
                           class="btn btn-sm {{ 'btn-danger' if admin.is_super_admin else 'btn-info' }}"
                           onclick="return confirm('确定要{{ '取消' if admin.is_super_admin else '设置为' }}超级管理员吗？')">
                            {{ '取消超管' if admin.is_super_admin else '设为超管' }}
                        </a>
                        {% endif %}
                    </div>
                    {% else %}
                    <span style="color: #999; font-size: 0.9rem;">当前用户</span>
                    {% endif %}
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<!-- 分页 -->
{% if admins.pages > 1 %}
<div class="pagination" style="margin-top: 2rem;">
    {% if admins.has_prev %}
        <a href="{{ url_for('admin.manage_admins', page=admins.prev_num) }}">‹ 上一页</a>
    {% endif %}
    
    {% for page_num in admins.iter_pages() %}
        {% if page_num %}
            {% if page_num != admins.page %}
                <a href="{{ url_for('admin.manage_admins', page=page_num) }}">{{ page_num }}</a>
            {% else %}
                <span class="current">{{ page_num }}</span>
            {% endif %}
        {% else %}
            <span>...</span>
        {% endif %}
    {% endfor %}
    
    {% if admins.has_next %}
        <a href="{{ url_for('admin.manage_admins', page=admins.next_num) }}">下一页 ›</a>
    {% endif %}
</div>
{% endif %}

{% else %}
<div class="card" style="text-align: center; padding: 3rem;">
    <h3 style="color: #666; margin-bottom: 1rem;">暂无管理员</h3>
    <p style="color: #999; margin-bottom: 2rem;">系统中还没有其他管理员</p>
    <a href="{{ url_for('admin.create_admin') }}" class="btn btn-primary">创建第一个管理员</a>
</div>
{% endif %}

<div style="text-align: center; margin-top: 2rem;">
    <a href="{{ url_for('admin.create_admin') }}" class="btn btn-primary">创建新管理员</a>
    <a href="{{ url_for('admin.system_settings') }}" class="btn btn-secondary">系统设置</a>
    <a href="{{ url_for('admin.dashboard') }}" class="btn btn-secondary">返回管理后台</a>
</div>
{% endblock %}
