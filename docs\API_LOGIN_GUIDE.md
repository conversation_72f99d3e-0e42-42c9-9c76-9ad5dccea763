# 点卡平台 - 登录API接口文档

## 📋 概述

点卡平台新增了完整的登录认证API接口，支持用户登录、令牌验证、登出和用户信息查询功能。

## 🔐 登录认证API

### 1. 用户登录

**接口地址**: `POST /api/login`

**请求参数**:
```json
{
    "username": "用户名",
    "password": "密码"
}
```

**成功响应**:
```json
{
    "success": true,
    "message": "登录成功",
    "data": {
        "user_id": 1,
        "username": "testuser",
        "role": "user",
        "points": 1000,
        "token": "a1b2c3d4e5f6...",
        "expires_at": "2024-01-02T12:00:00",
        "expires_in": 86400
    }
}
```

**失败响应**:
```json
{
    "success": false,
    "error": "用户名或密码错误",
    "error_code": "INVALID_CREDENTIALS"
}
```

### 2. 验证令牌

**接口地址**: `POST /api/verify_token`

**请求参数**:
```json
{
    "token": "用户令牌"
}
```

**或使用Header**:
```
Authorization: Bearer your_token_here
```

**成功响应**:
```json
{
    "success": true,
    "message": "令牌有效",
    "data": {
        "valid": true,
        "token": "a1b2c3d4e5f6..."
    }
}
```

### 3. 用户登出

**接口地址**: `POST /api/logout`

**请求参数**:
```json
{
    "token": "用户令牌"
}
```

**成功响应**:
```json
{
    "success": true,
    "message": "登出成功"
}
```

### 4. 获取用户信息

**接口地址**: `GET /api/user_info` 或 `POST /api/user_info`

**请求参数**:
```json
{
    "username": "用户名"
}
```

**成功响应**:
```json
{
    "success": true,
    "data": {
        "user_id": 1,
        "username": "testuser",
        "role": "user",
        "points": 1000,
        "created_at": "2024-01-01T10:00:00",
        "last_login": "2024-01-01T12:00:00"
    }
}
```

## 🔧 系统状态API

### API状态检查

**接口地址**: `GET /api/status`

**成功响应**:
```json
{
    "success": true,
    "message": "点卡平台API服务正常",
    "data": {
        "version": "1.0.0",
        "timestamp": "2024-01-01T12:00:00",
        "database": "connected",
        "mode": "bypass_sqlalchemy"
    }
}
```

## 🛠️ 原有API接口（需要API密钥）

以下接口需要在请求头中包含API密钥：
```
X-API-Key: your_api_key_here
```

### 1. 检查用户

**接口地址**: `POST /api/check_user`

### 2. 扣除点数

**接口地址**: `POST /api/deduct_points`

### 3. 获取余额

**接口地址**: `GET /api/get_balance` 或 `POST /api/get_balance`

### 4. 软件信息

**接口地址**: `GET /api/software_info`

### 5. 使用历史

**接口地址**: `GET /api/usage_history` 或 `POST /api/usage_history`

### 6. 获取验证挑战

**接口地址**: `GET /api/get_challenge`

## 📝 使用示例

### Python示例

```python
import requests
import json

# 基础URL
BASE_URL = "http://your-domain.com/api"

# 1. 用户登录
def login(username, password):
    url = f"{BASE_URL}/login"
    data = {
        "username": username,
        "password": password
    }
    response = requests.post(url, json=data)
    return response.json()

# 2. 获取用户信息
def get_user_info(username):
    url = f"{BASE_URL}/user_info"
    data = {"username": username}
    response = requests.post(url, json=data)
    return response.json()

# 3. 验证令牌
def verify_token(token):
    url = f"{BASE_URL}/verify_token"
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.post(url, headers=headers)
    return response.json()

# 使用示例
if __name__ == "__main__":
    # 登录
    result = login("testuser", "password123")
    if result["success"]:
        token = result["data"]["token"]
        print(f"登录成功，令牌: {token}")
        
        # 验证令牌
        verify_result = verify_token(token)
        print(f"令牌验证: {verify_result}")
        
        # 获取用户信息
        user_info = get_user_info("testuser")
        print(f"用户信息: {user_info}")
    else:
        print(f"登录失败: {result['error']}")
```

### JavaScript示例

```javascript
// 基础URL
const BASE_URL = "http://your-domain.com/api";

// 1. 用户登录
async function login(username, password) {
    const response = await fetch(`${BASE_URL}/login`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            username: username,
            password: password
        })
    });
    return await response.json();
}

// 2. 验证令牌
async function verifyToken(token) {
    const response = await fetch(`${BASE_URL}/verify_token`, {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
        }
    });
    return await response.json();
}

// 3. 获取用户信息
async function getUserInfo(username) {
    const response = await fetch(`${BASE_URL}/user_info`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            username: username
        })
    });
    return await response.json();
}

// 使用示例
(async () => {
    try {
        // 登录
        const loginResult = await login("testuser", "password123");
        if (loginResult.success) {
            const token = loginResult.data.token;
            console.log("登录成功，令牌:", token);
            
            // 验证令牌
            const verifyResult = await verifyToken(token);
            console.log("令牌验证:", verifyResult);
            
            // 获取用户信息
            const userInfo = await getUserInfo("testuser");
            console.log("用户信息:", userInfo);
        } else {
            console.log("登录失败:", loginResult.error);
        }
    } catch (error) {
        console.error("请求失败:", error);
    }
})();
```

## ⚠️ 注意事项

1. **令牌有效期**: 登录令牌有效期为24小时
2. **安全性**: 生产环境建议使用HTTPS
3. **错误处理**: 所有接口都返回统一的错误格式
4. **频率限制**: API接口有调用频率限制
5. **数据库**: 使用bypass_sqlalchemy模式，兼容性更好

## 🔄 错误代码说明

- `MISSING_CREDENTIALS`: 用户名或密码缺失
- `INVALID_CREDENTIALS`: 用户名或密码错误
- `MISSING_TOKEN`: 访问令牌缺失
- `TOKEN_VERIFICATION_FAILED`: 令牌验证失败
- `MISSING_USERNAME`: 用户名缺失
- `USER_NOT_FOUND`: 用户不存在
- `LOGIN_FAILED`: 登录失败
- `LOGOUT_FAILED`: 登出失败
- `GET_USER_INFO_FAILED`: 获取用户信息失败
