#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
用户页面测试脚本
测试用户相关页面是否正常工作
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app_bypass import app
from bypass_sqlalchemy import db_manager

def test_user_pages():
    """测试用户页面"""
    print("🧪 测试用户页面功能")
    print("=" * 50)
    
    with app.test_client() as client:
        # 1. 测试登录
        print("1️⃣ 测试用户登录...")
        login_data = {
            'username': 'testuser',
            'password': 'test123'
        }
        
        response = client.post('/auth/login', data=login_data, follow_redirects=True)
        if response.status_code == 200:
            print("   ✅ 登录成功")
        else:
            print("   ❌ 登录失败")
            return False
        
        # 2. 测试用户中心
        print("2️⃣ 测试用户中心...")
        response = client.get('/user/dashboard')
        if response.status_code == 200:
            print("   ✅ 用户中心页面正常")
        else:
            print(f"   ❌ 用户中心页面错误: {response.status_code}")
        
        # 3. 测试点数详情
        print("3️⃣ 测试点数详情...")
        response = client.get('/user/points')
        if response.status_code == 200:
            print("   ✅ 点数详情页面正常")
        else:
            print(f"   ❌ 点数详情页面错误: {response.status_code}")
        
        # 4. 测试使用历史
        print("4️⃣ 测试使用历史...")
        response = client.get('/user/history')
        if response.status_code == 200:
            print("   ✅ 使用历史页面正常")
        else:
            print(f"   ❌ 使用历史页面错误: {response.status_code}")
        
        # 5. 测试个人资料
        print("5️⃣ 测试个人资料...")
        response = client.get('/user/profile')
        if response.status_code == 200:
            print("   ✅ 个人资料页面正常")
        else:
            print(f"   ❌ 个人资料页面错误: {response.status_code}")
        
        # 6. 测试充值页面
        print("6️⃣ 测试充值页面...")
        response = client.get('/user/recharge')
        if response.status_code == 200:
            print("   ✅ 充值页面正常")
        else:
            print(f"   ❌ 充值页面错误: {response.status_code}")
        
        # 7. 测试软件列表
        print("7️⃣ 测试软件列表...")
        response = client.get('/user/software_list')
        if response.status_code == 200:
            print("   ✅ 软件列表页面正常")
        else:
            print(f"   ❌ 软件列表页面错误: {response.status_code}")
    
    print("\n🎉 用户页面测试完成！")
    return True

def check_database():
    """检查数据库状态"""
    print("🔍 检查数据库状态")
    print("=" * 50)
    
    try:
        with db_manager.get_connection() as conn:
            cursor = conn.cursor()
            
            # 检查用户表
            cursor.execute("SELECT COUNT(*) FROM user")
            user_count = cursor.fetchone()[0]
            print(f"   用户数量: {user_count}")
            
            # 检查软件表
            cursor.execute("SELECT COUNT(*) FROM software")
            software_count = cursor.fetchone()[0]
            print(f"   软件数量: {software_count}")
            
            # 检查消费记录表
            cursor.execute("SELECT COUNT(*) FROM consumption_record")
            record_count = cursor.fetchone()[0]
            print(f"   消费记录: {record_count}")
            
            # 检查卡密表
            cursor.execute("SELECT COUNT(*) FROM card")
            card_count = cursor.fetchone()[0]
            print(f"   卡密数量: {card_count}")
            
            print("   ✅ 数据库状态正常")
            return True
            
    except Exception as e:
        print(f"   ❌ 数据库检查失败: {e}")
        return False

def create_test_data():
    """创建测试数据"""
    print("📝 创建测试数据")
    print("=" * 50)
    
    try:
        with db_manager.get_connection() as conn:
            cursor = conn.cursor()
            
            # 创建测试软件
            cursor.execute("""
                INSERT OR IGNORE INTO software (name, description, points_per_use, is_active)
                VALUES (?, ?, ?, ?)
            """, ('测试软件', '这是一个测试软件', 10, 1))
            
            # 创建测试消费记录
            cursor.execute("""
                INSERT OR IGNORE INTO consumption_record 
                (user_id, software_id, points_consumed, ip_address, timestamp)
                VALUES (?, ?, ?, ?, datetime('now'))
            """, (3, 1, 10, '127.0.0.1'))  # testuser的ID通常是3
            
            conn.commit()
            print("   ✅ 测试数据创建成功")
            return True
            
    except Exception as e:
        print(f"   ❌ 测试数据创建失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始用户页面测试")
    print("=" * 60)
    
    # 检查数据库
    if not check_database():
        print("❌ 数据库检查失败，退出测试")
        sys.exit(1)
    
    # 创建测试数据
    create_test_data()
    
    # 测试用户页面
    if test_user_pages():
        print("\n✅ 所有测试通过！")
    else:
        print("\n❌ 测试失败！")
        sys.exit(1)
