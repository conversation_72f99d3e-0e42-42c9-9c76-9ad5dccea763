#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试修复后的用户页面
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app_bypass import app

def test_user_routes():
    """测试用户路由"""
    print("🧪 测试用户路由修复")
    print("=" * 50)
    
    with app.test_client() as client:
        # 登录
        login_data = {
            'username': 'testuser',
            'password': 'test123'
        }
        
        response = client.post('/auth/login', data=login_data, follow_redirects=True)
        if response.status_code != 200:
            print("❌ 登录失败")
            return False
        
        print("✅ 登录成功")
        
        # 测试各个页面
        test_pages = [
            ('/user/dashboard', '用户中心'),
            ('/user/points', '点数详情'),
            ('/user/history', '使用历史'),
            ('/user/profile', '个人资料'),
            ('/user/software_list', '软件列表'),
            ('/user/recharge', '充值页面')
        ]
        
        for url, name in test_pages:
            response = client.get(url)
            if response.status_code == 200:
                print(f"✅ {name} 页面正常")
            else:
                print(f"❌ {name} 页面错误: {response.status_code}")
                if response.status_code == 500:
                    print(f"   错误详情: {response.data.decode('utf-8')[:200]}...")
    
    print("\n🎉 路由测试完成！")

if __name__ == "__main__":
    test_user_routes()
