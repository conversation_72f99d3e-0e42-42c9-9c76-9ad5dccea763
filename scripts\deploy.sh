#!/bin/bash
# 部署脚本

echo "🚀 点卡平台部署脚本"
echo "===================="

# 检查Python版本
echo "检查Python环境..."
python3 --version
if [ $? -ne 0 ]; then
    echo "❌ Python3 未安装"
    exit 1
fi

# 创建虚拟环境
echo "创建虚拟环境..."
if [ ! -d "venv" ]; then
    python3 -m venv venv
    echo "✅ 虚拟环境创建成功"
else
    echo "✅ 虚拟环境已存在"
fi

# 激活虚拟环境
echo "激活虚拟环境..."
source venv/bin/activate

# 升级pip
echo "升级pip..."
pip install --upgrade pip

# 安装依赖
echo "安装依赖..."
pip install -r requirements.txt

# 创建必要目录
echo "创建目录结构..."
mkdir -p logs
mkdir -p instance
mkdir -p static/uploads

# 设置脚本权限
echo "设置脚本权限..."
chmod +x scripts/*.sh

# 初始化数据库
echo "初始化数据库..."
python3 -c "
from bypass_sqlalchemy import db_manager
db_manager.init_database()
print('✅ 数据库初始化完成')
"

# 创建默认管理员
echo "创建默认管理员..."
python3 tools/create_admin.py

echo ""
echo "🎉 部署完成！"
echo ""
echo "启动命令:"
echo "  开发环境: python3 run.py"
echo "  生产环境: ./scripts/start_production.sh"
echo ""
echo "访问地址:"
echo "  用户界面: http://localhost:5000"
echo "  管理后台: http://localhost:5000/admin"
echo ""
echo "默认账户:"
echo "  用户名: admin"
echo "  密码: admin123"
