#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
快速修复卡密价格字段问题
"""

import sys
import os
import sqlite3
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def quick_fix():
    """快速修复卡密价格字段"""
    print("🔧 快速修复卡密价格字段...")
    
    db_path = "point_card_platform.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查表结构
        cursor.execute("PRAGMA table_info(card)")
        columns = [column[1] for column in cursor.fetchall()]
        
        has_price = 'price' in columns
        has_currency_amount = 'currency_amount' in columns
        
        print(f"   当前字段: {columns}")
        print(f"   有 price 字段: {has_price}")
        print(f"   有 currency_amount 字段: {has_currency_amount}")
        
        if has_currency_amount and not has_price:
            print("   添加 price 字段...")
            cursor.execute("ALTER TABLE card ADD COLUMN price REAL")
            cursor.execute("UPDATE card SET price = currency_amount")
            print("   ✅ 已从 currency_amount 复制数据到 price")
            
        elif not has_price and not has_currency_amount:
            print("   添加 price 字段...")
            cursor.execute("ALTER TABLE card ADD COLUMN price REAL DEFAULT 0")
            print("   ✅ 已添加 price 字段")
            
        elif has_price and has_currency_amount:
            print("   同步 price 和 currency_amount 数据...")
            cursor.execute("UPDATE card SET price = currency_amount WHERE price IS NULL OR price = 0")
            print("   ✅ 已同步数据")
            
        elif has_price and not has_currency_amount:
            print("   price 字段已存在，无需修复")
        
        # 确保所有卡密都有价格数据
        cursor.execute("UPDATE card SET price = 1.0 WHERE price IS NULL OR price = 0")
        
        conn.commit()
        conn.close()
        
        print("✅ 卡密价格字段修复完成！")
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        return False

if __name__ == "__main__":
    if quick_fix():
        print("🎉 修复成功！现在可以正常访问卡密管理页面了。")
    else:
        print("💥 修复失败！请检查错误信息。")
        sys.exit(1)
