#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建管理员用户工具
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from bypass_sqlalchemy import db_manager
import hashlib
import getpass

def create_admin_user(username=None, password=None, email=None, is_super_admin=True):
    """创建管理员用户"""
    print("🔧 创建管理员用户")
    print("=" * 50)
    
    # 获取用户输入
    if not username:
        username = input("请输入用户名 (默认: admin): ").strip() or "admin"
    
    if not email:
        email = input(f"请输入邮箱 (默认: {username}@example.com): ").strip() or f"{username}@example.com"
    
    if not password:
        password = getpass.getpass("请输入密码 (默认: admin123): ") or "admin123"
        confirm_password = getpass.getpass("请确认密码: ") or "admin123"
        
        if password != confirm_password:
            print("❌ 密码不匹配")
            return False
    
    try:
        # 检查用户是否已存在
        existing_user = db_manager.get_user_by_username(username)
        if existing_user:
            print(f"⚠️  用户 '{username}' 已存在")
            choice = input("是否更新密码? (y/N): ").strip().lower()
            if choice == 'y':
                # 更新密码
                password_hash = hashlib.sha256(password.encode()).hexdigest()
                with db_manager.get_connection() as conn:
                    cursor = conn.cursor()
                    cursor.execute("""
                        UPDATE user SET password_hash = ?, is_admin = ?, is_super_admin = ?
                        WHERE username = ?
                    """, (password_hash, 1, 1 if is_super_admin else 0, username))
                    conn.commit()
                print(f"✅ 用户 '{username}' 密码已更新")
                return True
            else:
                return False
        
        # 创建新用户
        password_hash = hashlib.sha256(password.encode()).hexdigest()
        
        with db_manager.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                INSERT INTO user (username, email, password_hash, is_admin, is_super_admin, points, created_at)
                VALUES (?, ?, ?, ?, ?, ?, datetime('now'))
            """, (username, email, password_hash, 1, 1 if is_super_admin else 0, 10000))
            conn.commit()
            
        print(f"✅ 管理员用户创建成功!")
        print(f"   用户名: {username}")
        print(f"   邮箱: {email}")
        print(f"   角色: {'超级管理员' if is_super_admin else '管理员'}")
        print(f"   初始点数: 10000")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建用户失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def list_admin_users():
    """列出所有管理员用户"""
    print("\n📋 当前管理员用户:")
    print("-" * 50)
    
    try:
        with db_manager.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT username, email, is_admin, is_super_admin, points, created_at
                FROM user WHERE is_admin = 1 OR is_super_admin = 1
                ORDER BY is_super_admin DESC, created_at ASC
            """)
            users = cursor.fetchall()
            
            if not users:
                print("   没有管理员用户")
                return
            
            for user in users:
                user_dict = dict(user)
                role = "超级管理员" if user_dict['is_super_admin'] else "管理员"
                print(f"   {user_dict['username']} ({user_dict['email']}) - {role} - {user_dict['points']}点")
                
    except Exception as e:
        print(f"❌ 获取用户列表失败: {e}")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='管理员用户管理工具')
    parser.add_argument('--username', help='用户名')
    parser.add_argument('--password', help='密码')
    parser.add_argument('--email', help='邮箱')
    parser.add_argument('--admin-only', action='store_true', help='创建普通管理员(非超级管理员)')
    parser.add_argument('--list', action='store_true', help='列出所有管理员用户')
    
    args = parser.parse_args()
    
    if args.list:
        list_admin_users()
        return
    
    is_super_admin = not args.admin_only
    
    if create_admin_user(args.username, args.password, args.email, is_super_admin):
        list_admin_users()
        print(f"\n🎉 操作完成!")
    else:
        print(f"\n❌ 操作失败!")
        sys.exit(1)

if __name__ == "__main__":
    main()
