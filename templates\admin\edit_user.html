{% extends "base.html" %}

{% block title %}编辑用户 - 管理后台{% endblock %}

{% block content %}
<div class="card">
    <h2>编辑用户信息</h2>
    <p style="color: #e74c3c; font-weight: bold;">⚠️ 仅超级管理员可以编辑用户信息</p>
    
    <form method="POST">
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
            <div class="form-group">
                <label for="username">用户名:</label>
                <input type="text" id="username" name="username" class="form-control" 
                       value="{{ user.username }}" required>
                <small style="color: #666;">只能包含英文字母和数字，必须以字母开头</small>
            </div>
            
            <div class="form-group">
                <label for="email">邮箱:</label>
                <input type="email" id="email" name="email" class="form-control" 
                       value="{{ user.email }}" required>
            </div>
        </div>
        
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
            <div class="form-group">
                <label for="password">新密码:</label>
                <input type="password" id="password" name="password" class="form-control">
                <small style="color: #666;">留空则不修改密码，最少6位字符</small>
            </div>
            
            <div class="form-group">
                <label for="confirm_password">确认新密码:</label>
                <input type="password" id="confirm_password" name="confirm_password" class="form-control">
                <small style="color: #666;">请再次输入新密码</small>
            </div>
        </div>
        
        <div style="background: #f8f9fa; padding: 1rem; border-radius: 4px; margin: 1rem 0;">
            <h4 style="color: #2c3e50; margin: 0 0 0.5rem 0;">用户信息</h4>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; font-size: 0.9rem;">
                <div>
                    <strong>用户ID:</strong> {{ user.id }}
                </div>
                <div>
                    <strong>当前点数:</strong> {{ user.points }}
                </div>
                <div>
                    <strong>用户类型:</strong> 
                    {% if user.is_super_admin %}
                        <span style="color: #e74c3c;">超级管理员</span>
                    {% elif user.is_admin %}
                        <span style="color: #f39c12;">管理员</span>
                    {% else %}
                        <span style="color: #27ae60;">普通用户</span>
                    {% endif %}
                </div>
                <div>
                    <strong>注册时间:</strong> 
                    {% if user.created_at %}
                        {{ user.created_at.strftime('%Y-%m-%d %H:%M') if user.created_at.strftime else user.created_at }}
                    {% else %}
                        未知
                    {% endif %}
                </div>
                <div>
                    <strong>最后登录:</strong> 
                    {% if user.last_login %}
                        {{ user.last_login.strftime('%Y-%m-%d %H:%M') if user.last_login.strftime else user.last_login }}
                    {% else %}
                        从未登录
                    {% endif %}
                </div>
            </div>
        </div>
        
        <div style="display: flex; gap: 1rem;">
            <button type="submit" class="btn btn-primary">保存修改</button>
            <a href="{{ url_for('admin.users') }}" class="btn btn-secondary">取消</a>
        </div>
    </form>
</div>

<div class="card">
    <h3>⚠️ 重要提醒</h3>
    <ul style="color: #666; line-height: 1.6;">
        <li><strong>用户名修改:</strong> 修改后用户需要使用新用户名登录</li>
        <li><strong>密码修改:</strong> 如果修改密码，用户的当前登录状态将失效</li>
        <li><strong>邮箱修改:</strong> 请确保邮箱地址正确，用于密码重置等功能</li>
        <li><strong>权限管理:</strong> 用户权限请在"管理员管理"页面进行设置</li>
        <li><strong>点数管理:</strong> 用户点数请在"用户管理"页面进行调整</li>
    </ul>
</div>

<style>
.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #2c3e50;
}

.form-control {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e1e8ed;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
    text-align: center;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-2px);
}

.card {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.card h2, .card h3 {
    color: #2c3e50;
    margin-bottom: 1rem;
}

small {
    display: block;
    margin-top: 0.25rem;
}
</style>

<script>
// 密码确认验证
document.getElementById('confirm_password').addEventListener('input', function() {
    const password = document.getElementById('password').value;
    const confirmPassword = this.value;
    
    if (password && confirmPassword && password !== confirmPassword) {
        this.style.borderColor = '#e74c3c';
        this.style.boxShadow = '0 0 0 3px rgba(231, 76, 60, 0.1)';
    } else {
        this.style.borderColor = '#e1e8ed';
        this.style.boxShadow = 'none';
    }
});

// 用户名格式验证
document.getElementById('username').addEventListener('input', function() {
    const username = this.value;
    const pattern = /^[a-zA-Z][a-zA-Z0-9]*$/;
    
    if (username && !pattern.test(username)) {
        this.style.borderColor = '#e74c3c';
        this.style.boxShadow = '0 0 0 3px rgba(231, 76, 60, 0.1)';
    } else {
        this.style.borderColor = '#e1e8ed';
        this.style.boxShadow = 'none';
    }
});
</script>
{% endblock %}
