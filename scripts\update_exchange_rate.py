#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
更新汇率设置为1元=100点
确保超级管理员可以在后台设置汇率
"""

import sys
import os
import sqlite3
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def check_current_settings():
    """检查当前汇率设置"""
    print("🔍 检查当前汇率设置...")
    
    try:
        conn = sqlite3.connect('dianka.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # 检查系统设置表
        cursor.execute("SELECT * FROM system_settings WHERE key LIKE '%rate%' OR key LIKE '%yuan%'")
        settings = cursor.fetchall()
        
        print("   📋 当前汇率相关设置:")
        for setting in settings:
            print(f"      {setting['key']}: {setting['value']} ({setting['description']})")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"   ❌ 检查设置失败: {e}")
        return False

def update_exchange_rate():
    """更新汇率设置"""
    print("🔧 更新汇率设置...")
    
    try:
        conn = sqlite3.connect('dianka.db')
        cursor = conn.cursor()
        
        # 更新或插入 yuan_to_point_rate 设置
        cursor.execute("""
            INSERT OR REPLACE INTO system_settings (key, value, description, updated_at)
            VALUES (?, ?, ?, ?)
        """, ('yuan_to_point_rate', '100', '人民币到点数汇率 (1元=100点)', datetime.now().strftime('%Y-%m-%d %H:%M:%S')))
        
        # 更新或插入 points_per_yuan 设置（保持兼容性）
        cursor.execute("""
            INSERT OR REPLACE INTO system_settings (key, value, description, updated_at)
            VALUES (?, ?, ?, ?)
        """, ('points_per_yuan', '100', '每元人民币对应的点数', datetime.now().strftime('%Y-%m-%d %H:%M:%S')))
        
        conn.commit()
        conn.close()
        
        print("   ✅ 汇率设置更新成功")
        return True
        
    except Exception as e:
        print(f"   ❌ 更新汇率设置失败: {e}")
        return False

def verify_settings():
    """验证设置更新"""
    print("🔍 验证设置更新...")
    
    try:
        from bypass_sqlalchemy import DatabaseManager
        
        db_manager = DatabaseManager()
        
        # 测试获取汇率设置
        yuan_to_point_rate = db_manager.get_system_setting('yuan_to_point_rate', '10')
        points_per_yuan = db_manager.get_system_setting('points_per_yuan', '10')
        
        print(f"   📋 yuan_to_point_rate: {yuan_to_point_rate}")
        print(f"   📋 points_per_yuan: {points_per_yuan}")
        
        if yuan_to_point_rate == '100':
            print("   ✅ 汇率设置验证成功")
            return True
        else:
            print("   ❌ 汇率设置验证失败")
            return False
            
    except Exception as e:
        print(f"   ❌ 验证设置失败: {e}")
        return False

def test_card_creation():
    """测试卡密创建的汇率计算"""
    print("🔍 测试卡密创建汇率计算...")
    
    try:
        from bypass_sqlalchemy import DatabaseManager
        
        db_manager = DatabaseManager()
        
        # 模拟创建卡密的汇率计算
        yuan_to_point_rate = float(db_manager.get_system_setting('yuan_to_point_rate', '100'))
        
        test_amounts = [1, 5, 10, 50, 100]
        
        print("   📋 汇率计算测试:")
        for amount in test_amounts:
            points = amount * yuan_to_point_rate
            print(f"      {amount} 元 = {int(points)} 点")
        
        print("   ✅ 汇率计算测试成功")
        return True
        
    except Exception as e:
        print(f"   ❌ 汇率计算测试失败: {e}")
        return False

def check_admin_permissions():
    """检查超级管理员权限"""
    print("🔍 检查超级管理员权限...")
    
    try:
        conn = sqlite3.connect('dianka.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # 检查超级管理员用户
        cursor.execute("SELECT username, is_super_admin FROM user WHERE is_super_admin = 1")
        super_admins = cursor.fetchall()
        
        print("   📋 超级管理员用户:")
        for admin in super_admins:
            print(f"      {admin['username']} (超级管理员: {admin['is_super_admin']})")
        
        if len(super_admins) > 0:
            print("   ✅ 超级管理员权限检查通过")
            print("   ℹ️  超级管理员可以在 /admin/system_settings 页面设置汇率")
            conn.close()
            return True
        else:
            print("   ❌ 未找到超级管理员用户")
            conn.close()
            return False
            
    except Exception as e:
        print(f"   ❌ 检查超级管理员权限失败: {e}")
        return False

def show_usage_instructions():
    """显示使用说明"""
    print("\n📖 汇率设置使用说明")
    print("=" * 60)
    print("1. 🔑 超级管理员登录系统")
    print("2. 🔧 访问 /admin/system_settings 页面")
    print("3. 📝 在 '汇率设置' 部分修改 '每元对应点数'")
    print("4. 💾 点击 '保存设置' 按钮")
    print("5. ✅ 新的汇率将立即生效")
    print("\n💡 当前汇率: 1元 = 100点")
    print("💡 超级管理员可以随时调整汇率")
    print("💡 汇率变更会影响新创建的卡密，不影响已有卡密")

def main():
    """主函数"""
    print("🚀 开始更新汇率设置")
    print("=" * 60)
    
    results = []
    
    # 1. 检查当前设置
    results.append(("检查当前设置", check_current_settings()))
    
    # 2. 更新汇率设置
    results.append(("更新汇率设置", update_exchange_rate()))
    
    # 3. 验证设置更新
    results.append(("验证设置更新", verify_settings()))
    
    # 4. 测试汇率计算
    results.append(("测试汇率计算", test_card_creation()))
    
    # 5. 检查管理员权限
    results.append(("检查管理员权限", check_admin_permissions()))
    
    # 输出结果
    print("\n📊 更新结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 成功" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{total} 项检查通过")
    
    if passed == total:
        print("🎉 汇率设置更新完成！")
        show_usage_instructions()
        return True
    else:
        print("⚠️  部分检查失败，请检查系统配置。")
        return False

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n✅ 汇率更新完成！")
            sys.exit(0)
        else:
            print("\n❌ 汇率更新失败！")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⚠️  更新被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 更新过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
