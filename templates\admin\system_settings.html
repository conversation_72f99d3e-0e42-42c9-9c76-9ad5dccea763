{% extends "base.html" %}

{% block title %}系统设置 - 超级管理员{% endblock %}

{% block content %}
<style>
.super-admin-header {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    color: white;
    padding: 2rem;
    border-radius: 15px;
    margin-bottom: 2rem;
    text-align: center;
}

.settings-section {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.setting-group {
    margin-bottom: 2rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid #eee;
}

.setting-group:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.setting-title {
    color: #2c3e50;
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.setting-description {
    color: #666;
    margin-bottom: 1rem;
    line-height: 1.6;
}

.radio-group {
    display: flex;
    gap: 2rem;
    margin: 1rem 0;
}

.radio-option {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.security-warning {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
    padding: 1rem;
    border-radius: 8px;
    margin: 1rem 0;
}

.success-message {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
    padding: 1rem;
    border-radius: 8px;
    margin: 1rem 0;
}
</style>

<div class="super-admin-header">
    <h1>🔧 系统设置</h1>
    <p>超级管理员专用 - 配置系统核心参数和安全策略</p>
</div>

<form method="POST">
    <!-- 卡密管理设置 -->
    <div class="settings-section">
        <div class="setting-group">
            <h3 class="setting-title">💳 卡密管理设置</h3>
            <p class="setting-description">
                控制管理员创建卡密的权限和规则
            </p>
            
            <label><strong>卡密价值模式:</strong></label>
            <div class="radio-group">
                <div class="radio-option">
                    <input type="radio" name="card_value_mode" value="flexible" 
                           {{ 'checked' if settings.card_value_mode == 'flexible' else '' }}>
                    <label>自由设置 - 管理员可以设置任意面额</label>
                </div>
                <div class="radio-option">
                    <input type="radio" name="card_value_mode" value="fixed" 
                           {{ 'checked' if settings.card_value_mode == 'fixed' else '' }}>
                    <label>固定面额 - 只能创建预设面额的卡密</label>
                </div>
            </div>
            
            <div class="form-group">
                <label for="fixed_card_amounts">固定卡密金额 (逗号分隔):</label>
                <input type="text" id="fixed_card_amounts" name="fixed_card_amounts"
                       class="form-control" value="{{ settings.fixed_card_amounts }}"
                       placeholder="10,50,100,500,1000">
                <small style="color: #666;">仅在固定面额模式下生效，单位：元</small>
            </div>

            <div class="form-group">
                <label for="yuan_to_point_rate">汇率设置 (每元对应点数):</label>
                <input type="number" id="yuan_to_point_rate" name="yuan_to_point_rate"
                       class="form-control" value="{{ settings.yuan_to_point_rate }}"
                       step="1" min="1">
                <small style="color: #666;">例如: 100 表示 1元 = 100点</small>
            </div>
        </div>
    </div>

    <!-- API安全设置 -->
    <div class="settings-section">
        <div class="setting-group">
            <h3 class="setting-title">🔒 API安全设置</h3>
            <p class="setting-description">
                配置API接口的安全策略，防止恶意调用和破解
            </p>
            
            <div class="security-warning">
                <strong>⚠️ 安全提醒:</strong> 严格验证模式可以有效防止API被破解，但会增加开发者集成的复杂度。
            </div>
            
            <div class="form-group">
                <label for="api_rate_limit">API调用频率限制 (每分钟):</label>
                <input type="number" id="api_rate_limit" name="api_rate_limit" 
                       class="form-control" value="{{ settings.api_rate_limit }}" 
                       min="1" max="1000">
                <small style="color: #666;">防止恶意频繁调用API</small>
            </div>
            
            <label><strong>API验证模式:</strong></label>
            <div class="radio-group">
                <div class="radio-option">
                    <input type="radio" name="verification_mode" value="simple" 
                           {{ 'checked' if settings.verification_mode == 'simple' else '' }}>
                    <label>简单验证 - 仅验证API密钥</label>
                </div>
                <div class="radio-option">
                    <input type="radio" name="verification_mode" value="strict" 
                           {{ 'checked' if settings.verification_mode == 'strict' else '' }}>
                    <label>严格验证 - 需要时间戳和签名验证</label>
                </div>
            </div>
            
            <div class="security-warning">
                <strong>严格验证模式说明:</strong>
                <ul style="margin: 0.5rem 0 0 1rem;">
                    <li>开发者需要先调用 /api/get_challenge 获取挑战码</li>
                    <li>使用 HMAC-SHA256 算法生成签名</li>
                    <li>在扣费请求中包含挑战码、时间戳和签名</li>
                    <li>有效防止API被拦截和重放攻击</li>
                </ul>
            </div>
        </div>
    </div>

    <div style="text-align: center;">
        <button type="submit" class="btn btn-primary" style="padding: 1rem 3rem;">保存设置</button>
        <a href="{{ url_for('admin.dashboard') }}" class="btn btn-secondary" style="padding: 1rem 3rem;">返回管理后台</a>
    </div>
</form>

<!-- API使用示例 -->
<div class="settings-section">
    <h3 class="setting-title">📚 严格验证模式API示例</h3>
    <p class="setting-description">开发者在严格验证模式下的API调用示例</p>
    
    <h4 style="color: #2c3e50; margin: 1rem 0;">Python示例:</h4>
    <pre style="background: #f8f9fa; padding: 1rem; border-radius: 8px; overflow-x: auto;"><code>import requests
import hmac
import hashlib
import time

# 1. 获取挑战码
challenge_response = requests.get('http://localhost:5000/api/get_challenge', 
                                headers={'X-API-Key': 'your-api-key'})
challenge_data = challenge_response.json()['data']

# 2. 生成签名
challenge = challenge_data['challenge']
timestamp = challenge_data['timestamp']
signature = hmac.new(
    'your-api-key'.encode(),
    f"{challenge}:{timestamp}".encode(),
    hashlib.sha256
).hexdigest()

# 3. 扣费请求
headers = {
    'X-API-Key': 'your-api-key',
    'X-Challenge': challenge,
    'X-Timestamp': timestamp,
    'X-Response': signature,
    'Content-Type': 'application/json'
}

response = requests.post('http://localhost:5000/api/deduct_points',
                        headers=headers,
                        json={'username': 'testuser'})
</code></pre>
</div>
{% endblock %}
