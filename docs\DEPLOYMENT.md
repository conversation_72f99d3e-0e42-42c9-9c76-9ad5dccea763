# 点卡平台部署指南

## 🚀 快速部署（推荐方式）

### 使用Python虚拟环境部署

```bash
# 1. 创建项目目录
mkdir -p /root/dianka_platform
cd /root/dianka_platform

# 2. 上传所有项目文件到此目录

# 3. 创建Python虚拟环境
python3 -m venv venv

# 4. 激活虚拟环境
source venv/bin/activate

# 5. 安装依赖
pip install --upgrade pip
pip install -r requirements.txt

# 6. 启动应用
python run.py
```

### 一键部署脚本

```bash
# 创建并运行部署脚本
cat > deploy.sh << 'EOF'
#!/bin/bash
echo "开始部署点卡平台..."

# 创建目录
mkdir -p /root/dianka_platform
cd /root/dianka_platform

# 创建虚拟环境
python3 -m venv venv
source venv/bin/activate

# 安装依赖
pip install --upgrade pip
pip install -r requirements.txt

echo "部署完成！"
echo "启动命令: python run.py"
EOF

chmod +x deploy.sh
./deploy.sh
```

## 📋 系统要求

### 操作系统
- CentOS 7.6+ (64位)
- Ubuntu 18.04+
- Debian 9+
- 其他Linux发行版

### Python环境
- Python 3.6+
- pip 包管理器
- python3-venv (虚拟环境支持)

### 系统依赖
```bash
# CentOS/RHEL - 完整安装
yum install -y python3 python3-pip python3-venv python3-devel gcc-c++
yum groupinstall -y "Development Tools"

# Ubuntu/Debian
apt-get update
apt-get install -y python3 python3-pip python3-venv python3-dev build-essential
```

## 🔧 详细部署步骤

### 1. 环境准备

```bash
# 检查Python版本
python3 --version

# 检查pip版本
pip3 --version

# 安装虚拟环境支持（如果没有）
# CentOS: yum install python3-venv
# Ubuntu: apt-get install python3-venv
```

### 2. 项目部署

```bash
# 创建项目目录
mkdir -p /root/dianka_platform
cd /root/dianka_platform

# 上传项目文件（使用scp、rsync或其他方式）
# 确保以下文件已上传：
# - app_bypass.py
# - bypass_sqlalchemy.py
# - run.py
# - requirements.txt
# - routes/ 目录
# - templates/ 目录
# - static/ 目录

# 创建虚拟环境
python3 -m venv venv

# 激活虚拟环境
source venv/bin/activate

# 升级pip
pip install --upgrade pip

# 安装项目依赖
pip install -r requirements.txt
```

### 3. 数据库初始化

```bash
# 启动应用（首次启动会自动创建数据库）
python run.py

# 或者手动初始化数据库
python -c "from bypass_sqlalchemy import db_manager; db_manager.init_database()"
```

### 4. 创建管理员账户

```bash
# 使用管理工具创建管理员
python tools/create_admin.py

# 或者手动创建
python -c "
from bypass_sqlalchemy import db_manager
import hashlib
password_hash = hashlib.sha256('admin123'.encode()).hexdigest()
with db_manager.get_connection() as conn:
    cursor = conn.cursor()
    cursor.execute('''
        INSERT INTO user (username, email, password_hash, is_admin, is_super_admin, points, created_at)
        VALUES (?, ?, ?, ?, ?, ?, datetime('now'))
    ''', ('admin', '<EMAIL>', password_hash, 1, 1, 10000))
    conn.commit()
print('管理员账户创建成功: admin/admin123')
"
```

## 🏭 生产环境部署

### 使用Gunicorn部署

```bash
# 安装Gunicorn
pip install gunicorn

# 启动生产服务器
gunicorn --bind 0.0.0.0:5000 --workers 4 app_bypass:app

# 或使用提供的启动脚本
chmod +x scripts/start_production.sh
./scripts/start_production.sh
```

### 使用systemd服务

```bash
# 创建服务文件
cat > /etc/systemd/system/dianka-platform.service << 'EOF'
[Unit]
Description=Dianka Platform
After=network.target

[Service]
Type=forking
User=root
WorkingDirectory=/root/dianka_platform
Environment=PATH=/root/dianka_platform/venv/bin
ExecStart=/root/dianka_platform/scripts/start_production.sh
ExecStop=/root/dianka_platform/scripts/stop_production.sh
Restart=always

[Install]
WantedBy=multi-user.target
EOF

# 启用并启动服务
systemctl daemon-reload
systemctl enable dianka-platform
systemctl start dianka-platform

# 查看服务状态
systemctl status dianka-platform
```

### Nginx反向代理配置

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /static {
        alias /root/dianka_platform/static;
        expires 30d;
    }
}
```

## 🔒 HTTPS配置

### 使用Let's Encrypt

```bash
# 安装certbot
yum install -y certbot python3-certbot-nginx  # CentOS
# apt-get install -y certbot python3-certbot-nginx  # Ubuntu

# 获取SSL证书
certbot --nginx -d your-domain.com

# 自动续期
echo "0 12 * * * /usr/bin/certbot renew --quiet" | crontab -
```

### Nginx HTTPS配置

```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;

    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto https;
    }
}

server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}
```

## 🔧 配置优化

### 性能优化

```bash
# 调整Gunicorn配置
gunicorn --bind 0.0.0.0:5000 \
         --workers 4 \
         --worker-class sync \
         --timeout 120 \
         --keepalive 2 \
         --max-requests 1000 \
         --max-requests-jitter 100 \
         app_bypass:app
```

### 日志配置

```bash
# 创建日志目录
mkdir -p /root/dianka_platform/logs

# 配置日志轮转
cat > /etc/logrotate.d/dianka-platform << 'EOF'
/root/dianka_platform/logs/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 root root
    postrotate
        systemctl reload dianka-platform
    endscript
}
EOF
```

## 🛠️ 故障排除

### 常见问题

1. **端口占用**
```bash
# 查看端口占用
lsof -i :5000
netstat -tlnp | grep :5000

# 停止占用进程
kill -9 <PID>
```

2. **权限问题**
```bash
# 检查文件权限
ls -la /root/dianka_platform/

# 修复权限
chmod +x scripts/*.sh
chown -R root:root /root/dianka_platform/
```

3. **依赖安装失败**
```bash
# 安装编译环境（CentOS/RHEL）
yum install -y gcc-c++ python3-devel
yum groupinstall -y "Development Tools"

# 升级pip
pip install --upgrade pip

# 重新安装依赖
pip install -r requirements.txt --force-reinstall

# 检查Python路径
which python3
which pip3
```

4. **数据库问题**
```bash
# 检查数据库文件
ls -la point_card_platform.db

# 重新初始化数据库
python tools/database_manager.py --reset
```

### 日志查看

```bash
# 查看应用日志
tail -f logs/error.log
tail -f logs/access.log

# 查看系统服务日志
journalctl -u dianka-platform -f

# 查看Nginx日志
tail -f /var/log/nginx/error.log
tail -f /var/log/nginx/access.log
```

## 📊 监控和维护

### 健康检查

```bash
# API状态检查
curl http://localhost:5000/api/status

# 服务状态检查
systemctl status dianka-platform

# 进程检查
ps aux | grep gunicorn
```

### 备份策略

```bash
# 数据库备份
python tools/database_manager.py --backup

# 完整备份脚本
cat > backup.sh << 'EOF'
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/root/backups/dianka_platform_$DATE"

mkdir -p $BACKUP_DIR
cp -r /root/dianka_platform/* $BACKUP_DIR/
tar -czf "/root/backups/dianka_platform_$DATE.tar.gz" -C /root/backups "dianka_platform_$DATE"
rm -rf $BACKUP_DIR

echo "备份完成: /root/backups/dianka_platform_$DATE.tar.gz"
EOF

chmod +x backup.sh
```

### 定期维护

```bash
# 添加到crontab
crontab -e

# 每天凌晨2点备份
0 2 * * * /root/dianka_platform/backup.sh

# 每周清理日志
0 3 * * 0 /root/dianka_platform/tools/database_manager.py --clean
```

## 🎯 部署检查清单

- [ ] Python 3.6+ 已安装
- [ ] 虚拟环境已创建并激活
- [ ] 所有依赖已安装
- [ ] 数据库已初始化
- [ ] 管理员账户已创建
- [ ] 应用可以正常启动
- [ ] API接口可以正常访问
- [ ] 生产环境配置已完成
- [ ] Nginx反向代理已配置
- [ ] HTTPS证书已配置
- [ ] 系统服务已配置
- [ ] 日志轮转已配置
- [ ] 备份策略已实施
- [ ] 监控检查已设置

## 📞 技术支持

如果在部署过程中遇到问题，请：

1. 检查系统日志和应用日志
2. 确认所有依赖已正确安装
3. 验证网络和防火墙配置
4. 参考故障排除部分
5. 联系技术支持团队
