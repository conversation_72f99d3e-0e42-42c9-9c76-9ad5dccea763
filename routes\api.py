from flask import Blueprint, request, jsonify
from datetime import datetime, timedelta
from functools import wraps
import hashlib
import hmac
import time
import secrets
from bypass_sqlalchemy import DatabaseManager

api_bp = Blueprint('api', __name__)

# 初始化数据库管理器
db_manager = DatabaseManager()

# API调用频率限制
api_call_history = {}

def log_api_call(api_key, endpoint, success, user_id=None, error_message=None):
    """记录API调用日志"""
    try:
        db_manager.log_api_call(
            api_key=api_key,
            endpoint=endpoint,
            user_id=user_id,
            success=success,
            error_message=error_message,
            ip_address=request.remote_addr
        )
    except Exception as e:
        print(f"记录API日志失败: {e}")

def check_rate_limit(api_key):
    """检查API调用频率限制"""
    try:
        rate_limit = int(db_manager.get_system_setting('api_rate_limit', '100'))
    except:
        rate_limit = 100
    
    current_time = datetime.now()

    if api_key not in api_call_history:
        api_call_history[api_key] = []

    # 清理1分钟前的记录
    api_call_history[api_key] = [
        call_time for call_time in api_call_history[api_key]
        if current_time - call_time < timedelta(minutes=1)
    ]

    # 检查是否超过限制
    if len(api_call_history[api_key]) >= rate_limit:
        return False

    # 记录本次调用
    api_call_history[api_key].append(current_time)
    return True

def generate_challenge():
    """生成验证挑战"""
    return secrets.token_hex(16)

def verify_challenge_response(challenge, response, api_key, timestamp):
    """验证挑战响应"""
    # 检查时间戳（5分钟内有效）
    try:
        request_time = datetime.fromtimestamp(float(timestamp))
        if datetime.utcnow() - request_time > timedelta(minutes=5):
            return False
    except:
        return False

    # 验证签名
    expected_response = hmac.new(
        api_key.encode(),
        f"{challenge}:{timestamp}".encode(),
        hashlib.sha256
    ).hexdigest()

    return hmac.compare_digest(expected_response, response)

def require_api_key(f):
    """API密钥验证装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        api_key = request.headers.get('X-API-Key') or request.form.get('api_key') or request.args.get('api_key')

        if not api_key:
            return jsonify({
                'success': False,
                'error': 'API密钥缺失',
                'error_code': 'MISSING_API_KEY'
            }), 401

        # 检查频率限制
        if not check_rate_limit(api_key):
            log_api_call(api_key, request.endpoint, False, error_message='API调用频率超限')
            return jsonify({
                'success': False,
                'error': 'API调用频率超限，请稍后重试',
                'error_code': 'RATE_LIMIT_EXCEEDED'
            }), 429

        software = db_manager.get_software_by_api_key(api_key)
        if not software:
            log_api_call(api_key, request.endpoint, False, error_message='无效的API密钥')
            return jsonify({
                'success': False,
                'error': '无效的API密钥',
                'error_code': 'INVALID_API_KEY'
            }), 401

        if not software.get('is_active', True):
            log_api_call(api_key, request.endpoint, False, error_message='软件已被禁用')
            return jsonify({
                'success': False,
                'error': '软件已被禁用',
                'error_code': 'SOFTWARE_DISABLED'
            }), 403

        # 严格验证模式
        try:
            verification_mode = db_manager.get_system_setting('verification_mode', 'simple')
        except:
            verification_mode = 'simple'
            
        if verification_mode == 'strict' and request.endpoint in ['api.deduct_points']:
            challenge = request.headers.get('X-Challenge')
            response = request.headers.get('X-Response')
            timestamp = request.headers.get('X-Timestamp')

            if not all([challenge, response, timestamp]):
                log_api_call(api_key, request.endpoint, False, error_message='缺少安全验证参数')
                return jsonify({
                    'success': False,
                    'error': '缺少安全验证参数',
                    'error_code': 'MISSING_SECURITY_PARAMS'
                }), 401

            if not verify_challenge_response(challenge, response, api_key, timestamp):
                log_api_call(api_key, request.endpoint, False, error_message='安全验证失败')
                return jsonify({
                    'success': False,
                    'error': '安全验证失败',
                    'error_code': 'SECURITY_VERIFICATION_FAILED'
                }), 401

        # 将软件信息传递给视图函数
        kwargs['software'] = software
        return f(*args, **kwargs)

    return decorated_function

# ==================== 系统状态API ====================

@api_bp.route('/status', methods=['GET'])
def api_status():
    """API系统状态"""
    return jsonify({
        'success': True,
        'message': '点卡平台API服务正常',
        'data': {
            'version': '1.0.0',
            'timestamp': datetime.now().isoformat(),
            'database': 'connected',
            'mode': 'bypass_sqlalchemy'
        }
    })

# ==================== 登录认证API ====================

@api_bp.route('/login', methods=['POST'])
def api_login():
    """用户登录API接口"""
    try:
        # 获取请求数据
        data = request.get_json() or {}
        username = data.get('username') or request.form.get('username')
        password = data.get('password') or request.form.get('password')
        
        # 验证必填参数
        if not username or not password:
            return jsonify({
                'success': False,
                'error': '用户名和密码不能为空',
                'error_code': 'MISSING_CREDENTIALS'
            }), 400
        
        # 验证用户
        user = db_manager.get_user_by_username(username)
        if not user:
            return jsonify({
                'success': False,
                'error': '用户名或密码错误',
                'error_code': 'INVALID_CREDENTIALS'
            }), 401
        
        # 验证密码
        if not db_manager.verify_password(password, user['password_hash']):
            return jsonify({
                'success': False,
                'error': '用户名或密码错误',
                'error_code': 'INVALID_CREDENTIALS'
            }), 401
        
        # 生成访问令牌（简单的token，实际项目中建议使用JWT）
        token = secrets.token_hex(32)
        expires_at = datetime.now() + timedelta(hours=24)  # 24小时有效期
        
        # 更新最后登录时间
        try:
            db_manager.update_user_last_login(user['id'])
        except:
            pass
        
        # 确定用户角色
        if user.get('is_super_admin'):
            role = 'super_admin'
        elif user.get('is_admin'):
            role = 'admin'
        else:
            role = 'user'

        return jsonify({
            'success': True,
            'message': '登录成功',
            'data': {
                'user_id': user['id'],
                'username': user['username'],
                'role': role,
                'points': user['points'],
                'token': token,
                'expires_at': expires_at.isoformat(),
                'expires_in': 86400  # 24小时，单位秒
            }
        })
        
    except Exception as e:
        # 添加详细的错误日志
        import traceback
        error_details = str(e)
        print(f"登录API异常: {error_details}")
        print(f"异常堆栈: {traceback.format_exc()}")

        return jsonify({
            'success': False,
            'error': '登录失败，请重试',
            'error_code': 'LOGIN_FAILED'
        }), 500

@api_bp.route('/verify_token', methods=['POST'])
def verify_token():
    """验证访问令牌"""
    try:
        data = request.get_json() or {}
        token = data.get('token') or request.headers.get('Authorization', '').replace('Bearer ', '')
        
        if not token:
            return jsonify({
                'success': False,
                'error': '访问令牌缺失',
                'error_code': 'MISSING_TOKEN'
            }), 401
        
        # 这里简化处理，实际项目中应该从数据库验证token
        # 可以扩展实现token的存储和验证逻辑
        
        return jsonify({
            'success': True,
            'message': '令牌有效',
            'data': {
                'valid': True,
                'token': token
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': '令牌验证失败',
            'error_code': 'TOKEN_VERIFICATION_FAILED'
        }), 500

@api_bp.route('/logout', methods=['POST'])
def api_logout():
    """用户登出API接口"""
    try:
        data = request.get_json() or {}
        token = data.get('token') or request.headers.get('Authorization', '').replace('Bearer ', '')
        
        # 这里可以实现token的失效逻辑
        # 实际项目中应该从数据库中删除或标记token为无效
        
        return jsonify({
            'success': True,
            'message': '登出成功'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': '登出失败',
            'error_code': 'LOGOUT_FAILED'
        }), 500

@api_bp.route('/user_info', methods=['GET', 'POST'])
def get_user_info():
    """获取用户信息API接口"""
    try:
        # 获取用户标识
        username = request.args.get('username') or request.form.get('username')
        if request.is_json:
            data = request.get_json() or {}
            username = username or data.get('username')
        
        if not username:
            return jsonify({
                'success': False,
                'error': '用户名缺失',
                'error_code': 'MISSING_USERNAME'
            }), 400
        
        # 获取用户信息
        user = db_manager.get_user_by_username(username)
        if not user:
            return jsonify({
                'success': False,
                'error': '用户不存在',
                'error_code': 'USER_NOT_FOUND'
            }), 404
        
        # 确定用户角色
        if user.get('is_super_admin'):
            role = 'super_admin'
        elif user.get('is_admin'):
            role = 'admin'
        else:
            role = 'user'

        return jsonify({
            'success': True,
            'data': {
                'user_id': user['id'],
                'username': user['username'],
                'role': role,
                'points': user['points'],
                'created_at': user['created_at'],
                'last_login': user.get('last_login')
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': '获取用户信息失败',
            'error_code': 'GET_USER_INFO_FAILED'
        }), 500

# ==================== 原有API接口 ====================

@api_bp.route('/get_challenge', methods=['GET'])
@require_api_key
def get_challenge(software):
    """获取验证挑战（用于严格验证模式）"""
    challenge = generate_challenge()
    timestamp = str(time.time())

    log_api_call(software.get('api_key'), 'get_challenge', True)
    return jsonify({
        'success': True,
        'data': {
            'challenge': challenge,
            'timestamp': timestamp,
            'expires_in': 300  # 5分钟
        }
    })

@api_bp.route('/check_user', methods=['POST'])
@require_api_key
def check_user(software):
    """检查用户是否存在并返回点数信息"""
    data = request.get_json() or {}
    username = data.get('username') or request.form.get('username')

    if not username:
        log_api_call(software.get('api_key'), 'check_user', False, error_message='用户名缺失')
        return jsonify({
            'success': False,
            'error': '用户名缺失',
            'error_code': 'MISSING_USERNAME'
        }), 400

    user = db_manager.get_user_by_username(username)
    if not user:
        log_api_call(software.get('api_key'), 'check_user', False, error_message='用户不存在')
        return jsonify({
            'success': False,
            'error': '用户不存在',
            'error_code': 'USER_NOT_FOUND'
        }), 404

    log_api_call(software.get('api_key'), 'check_user', True, user['id'])
    return jsonify({
        'success': True,
        'data': {
            'user_id': user['id'],
            'username': user['username'],
            'points': user['points'],
            'can_use': user['points'] >= software.get('points_per_use', 1)
        }
    })

@api_bp.route('/deduct_points', methods=['POST'])
@require_api_key
def deduct_points(software):
    """扣除用户点数"""
    data = request.get_json() or {}
    username = data.get('username') or request.form.get('username')
    custom_points = data.get('points') or request.form.get('points')

    if not username:
        log_api_call(software.get('api_key'), 'deduct_points', False, error_message='用户名缺失')
        return jsonify({
            'success': False,
            'error': '用户名缺失',
            'error_code': 'MISSING_USERNAME'
        }), 400

    # 确定扣除的点数
    points_to_deduct = software.get('points_per_use', 1)
    if custom_points:
        try:
            custom_points = int(custom_points)
            if custom_points > 0:
                points_to_deduct = custom_points
        except ValueError:
            pass

    user = db_manager.get_user_by_username(username)
    if not user:
        log_api_call(software.get('api_key'), 'deduct_points', False, error_message='用户不存在')
        return jsonify({
            'success': False,
            'error': '用户不存在',
            'error_code': 'USER_NOT_FOUND'
        }), 404

    if user['points'] < points_to_deduct:
        log_api_call(software.get('api_key'), 'deduct_points', False, user['id'], '点数不足')
        return jsonify({
            'success': False,
            'error': '点数不足',
            'error_code': 'INSUFFICIENT_POINTS',
            'data': {
                'current_points': user['points'],
                'required_points': points_to_deduct
            }
        }), 400

    try:
        # 扣除点数并记录消费
        record_id = db_manager.deduct_user_points(
            user_id=user['id'],
            points=points_to_deduct,
            software_id=software['id'],
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent', '')
        )

        # 获取更新后的用户信息
        updated_user = db_manager.get_user_by_id(user['id'])

        log_api_call(software.get('api_key'), 'deduct_points', True, user['id'])

        return jsonify({
            'success': True,
            'data': {
                'user_id': user['id'],
                'username': user['username'],
                'points_deducted': points_to_deduct,
                'remaining_points': updated_user['points'],
                'record_id': record_id
            }
        })

    except Exception as e:
        log_api_call(software.get('api_key'), 'deduct_points', False, user['id'], str(e))
        return jsonify({
            'success': False,
            'error': '扣费失败，请重试',
            'error_code': 'DEDUCTION_FAILED'
        }), 500

@api_bp.route('/get_balance', methods=['GET', 'POST'])
@require_api_key
def get_balance(software):
    """获取用户余额"""
    username = request.args.get('username') or request.form.get('username')
    if request.is_json:
        data = request.get_json() or {}
        username = username or data.get('username')

    if not username:
        log_api_call(software.get('api_key'), 'get_balance', False, error_message='用户名缺失')
        return jsonify({
            'success': False,
            'error': '用户名缺失',
            'error_code': 'MISSING_USERNAME'
        }), 400

    user = db_manager.get_user_by_username(username)
    if not user:
        log_api_call(software.get('api_key'), 'get_balance', False, error_message='用户不存在')
        return jsonify({
            'success': False,
            'error': '用户不存在',
            'error_code': 'USER_NOT_FOUND'
        }), 404

    log_api_call(software.get('api_key'), 'get_balance', True, user['id'])
    return jsonify({
        'success': True,
        'data': {
            'user_id': user['id'],
            'username': user['username'],
            'points': user['points']
        }
    })

@api_bp.route('/software_info', methods=['GET'])
@require_api_key
def software_info(software):
    """获取软件信息"""
    log_api_call(software.get('api_key'), 'software_info', True)
    return jsonify({
        'success': True,
        'data': {
            'software_id': software['id'],
            'name': software['name'],
            'description': software.get('description', ''),
            'points_per_use': software.get('points_per_use', 1),
            'is_active': software.get('is_active', True)
        }
    })

@api_bp.route('/usage_history', methods=['GET', 'POST'])
@require_api_key
def usage_history(software):
    """获取软件使用历史"""
    username = request.args.get('username') or request.form.get('username')
    limit = request.args.get('limit', 50, type=int)
    limit = min(limit, 100)  # 最多返回100条记录

    if request.is_json:
        data = request.get_json() or {}
        username = username or data.get('username')
        limit = data.get('limit', limit)

    try:
        records = db_manager.get_consumption_records(
            software_id=software['id'],
            username=username,
            limit=limit
        )

        log_api_call(software.get('api_key'), 'usage_history', True)
        return jsonify({
            'success': True,
            'data': records,
            'total_records': len(records)
        })

    except Exception as e:
        log_api_call(software.get('api_key'), 'usage_history', False, error_message=str(e))
        return jsonify({
            'success': False,
            'error': '获取使用历史失败',
            'error_code': 'GET_HISTORY_FAILED'
        }), 500
