#!/usr/bin/env python3
"""
修复版本的用户路由 - 绕过Flask-SQLAlchemy
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from datetime import datetime
import secrets
import string

# 创建蓝图
user_bp = Blueprint('user', __name__)

# 导入绕过方案
from bypass_sqlalchemy import db_manager

@user_bp.route('/dashboard')
@login_required
def dashboard():
    """用户仪表板"""
    try:
        # 获取用户最新信息
        user_data = db_manager.get_user_by_id(current_user.id)
        
        # 获取最近的消费记录
        with db_manager.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT cr.*, s.name as software_name
                FROM consumption_record cr
                LEFT JOIN software s ON cr.software_id = s.id
                WHERE cr.user_id = ?
                ORDER BY cr.timestamp DESC
                LIMIT 10
            """, (current_user.id,))
            recent_records = cursor.fetchall()
            
            # 计算总消费
            cursor.execute("""
                SELECT COALESCE(SUM(points_consumed), 0)
                FROM consumption_record
                WHERE user_id = ?
            """, (current_user.id,))
            total_consumed = cursor.fetchone()[0]
        
        return render_template('user/dashboard.html', 
                             user=user_data,
                             recent_records=recent_records,
                             total_consumed=total_consumed)
                             
    except Exception as e:
        flash(f'获取仪表板数据失败: {str(e)}', 'error')
        return render_template('user/dashboard.html', 
                             user=current_user,
                             recent_records=[],
                             total_consumed=0)

@user_bp.route('/recharge', methods=['GET', 'POST'])
@login_required
def recharge():
    """点数充值"""
    if request.method == 'GET':
        return render_template('user/recharge.html')
    
    try:
        card_code = request.form.get('card_code', '').strip()
        
        if not card_code:
            flash('请输入卡密', 'error')
            return render_template('user/recharge.html')
        
        # 使用卡密
        success, message = db_manager.use_card(card_code, current_user.id)
        
        if success:
            flash(message, 'success')
            return redirect(url_for('user.dashboard'))
        else:
            flash(message, 'error')
            return render_template('user/recharge.html')
            
    except Exception as e:
        flash(f'充值失败: {str(e)}', 'error')
        return render_template('user/recharge.html')

@user_bp.route('/points')
@login_required
def points():
    """点数详情"""
    try:
        # 获取用户信息
        user_data = db_manager.get_user_by_id(current_user.id)
        
        # 获取充值记录
        with db_manager.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT card_code, points_value, used_at
                FROM card
                WHERE used_by = ?
                ORDER BY used_at DESC
            """, (current_user.id,))
            recharge_records = cursor.fetchall()
            
            # 获取消费记录
            cursor.execute("""
                SELECT cr.*, s.name as software_name
                FROM consumption_record cr
                LEFT JOIN software s ON cr.software_id = s.id
                WHERE cr.user_id = ?
                ORDER BY cr.timestamp DESC
            """, (current_user.id,))
            consumption_records = cursor.fetchall()
        
        return render_template('user/points.html',
                             user=user_data,
                             recharge_records=recharge_records,
                             consumption_records=consumption_records)
                             
    except Exception as e:
        flash(f'获取点数信息失败: {str(e)}', 'error')
        return redirect(url_for('user.dashboard'))

@user_bp.route('/profile', methods=['GET', 'POST'])
@login_required
def profile():
    """用户资料"""
    if request.method == 'GET':
        user_data = db_manager.get_user_by_id(current_user.id)
        return render_template('user/profile.html', user=user_data)
    
    try:
        # 更新用户信息
        email = request.form.get('email', '').strip()
        
        if not email:
            flash('邮箱不能为空', 'error')
            return render_template('user/profile.html', user=current_user)
        
        # 检查邮箱是否已被其他用户使用
        with db_manager.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT id FROM user WHERE email = ? AND id != ?
            """, (email, current_user.id))
            existing_user = cursor.fetchone()
            
            if existing_user:
                flash('该邮箱已被其他用户使用', 'error')
                return render_template('user/profile.html', user=current_user)
            
            # 更新邮箱
            cursor.execute("""
                UPDATE user SET email = ? WHERE id = ?
            """, (email, current_user.id))
            conn.commit()
        
        flash('资料更新成功', 'success')
        return redirect(url_for('user.profile'))
        
    except Exception as e:
        flash(f'更新失败: {str(e)}', 'error')
        return render_template('user/profile.html', user=current_user)

@user_bp.route('/history')
@login_required
def history():
    """使用历史"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = 20
        offset = (page - 1) * per_page
        
        with db_manager.get_connection() as conn:
            cursor = conn.cursor()
            
            # 获取总记录数
            cursor.execute("""
                SELECT COUNT(*) FROM consumption_record WHERE user_id = ?
            """, (current_user.id,))
            total = cursor.fetchone()[0]
            
            # 获取分页记录
            cursor.execute("""
                SELECT cr.*, s.name as software_name
                FROM consumption_record cr
                LEFT JOIN software s ON cr.software_id = s.id
                WHERE cr.user_id = ?
                ORDER BY cr.timestamp DESC
                LIMIT ? OFFSET ?
            """, (current_user.id, per_page, offset))
            records = cursor.fetchall()
        
        # 计算分页信息
        total_pages = (total + per_page - 1) // per_page
        has_prev = page > 1
        has_next = offset + per_page < total
        prev_num = page - 1 if has_prev else None
        next_num = page + 1 if has_next else None

        return render_template('user/history.html',
                             records=records,
                             page=page,
                             total=total,
                             total_pages=total_pages,
                             has_prev=has_prev,
                             has_next=has_next,
                             prev_num=prev_num,
                             next_num=next_num)
                             
    except Exception as e:
        flash(f'获取历史记录失败: {str(e)}', 'error')
        return redirect(url_for('user.dashboard'))

@user_bp.route('/software_list')
@login_required
def software_list():
    """软件列表"""
    try:
        # 获取所有启用的软件
        with db_manager.get_connection() as conn:
            cursor = conn.cursor()

            # 首先检查表结构
            cursor.execute("PRAGMA table_info(software)")
            columns = [column[1] for column in cursor.fetchall()]

            # 根据可用列构建查询
            if 'description' in columns:
                cursor.execute("""
                    SELECT id, name, description, points_per_use, is_active
                    FROM software
                    WHERE is_active = 1
                    ORDER BY name
                """)
            else:
                cursor.execute("""
                    SELECT id, name, '' as description, points_per_use, is_active
                    FROM software
                    WHERE is_active = 1
                    ORDER BY name
                """)

            software_list = cursor.fetchall()

        return render_template('user/software_list.html', software_list=software_list)

    except Exception as e:
        flash(f'获取软件列表失败: {str(e)}', 'error')
        return render_template('user/software_list.html', software_list=[])

# API端点
@user_bp.route('/api/points')
@login_required
def api_points():
    """获取用户点数API"""
    try:
        user_data = db_manager.get_user_by_id(current_user.id)
        return jsonify({
            'success': True,
            'points': user_data['points'],
            'username': user_data['username']
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': str(e)
        })

@user_bp.route('/api/recharge', methods=['POST'])
@login_required
def api_recharge():
    """充值API"""
    try:
        data = request.get_json()
        card_code = data.get('card_code', '').strip()
        
        if not card_code:
            return jsonify({
                'success': False,
                'message': '请输入卡密'
            })
        
        success, message = db_manager.use_card(card_code, current_user.id)
        
        if success:
            # 获取更新后的用户信息
            user_data = db_manager.get_user_by_id(current_user.id)
            return jsonify({
                'success': True,
                'message': message,
                'new_points': user_data['points']
            })
        else:
            return jsonify({
                'success': False,
                'message': message
            })
            
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'充值失败: {str(e)}'
        })

@user_bp.route('/api/history')
@login_required
def api_history():
    """获取使用历史API"""
    try:
        limit = request.args.get('limit', 10, type=int)
        
        with db_manager.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT cr.points_consumed, cr.timestamp, cr.ip_address,
                       s.name as software_name
                FROM consumption_record cr
                LEFT JOIN software s ON cr.software_id = s.id
                WHERE cr.user_id = ?
                ORDER BY cr.timestamp DESC
                LIMIT ?
            """, (current_user.id, limit))
            records = cursor.fetchall()
        
        # 转换为字典列表
        history = []
        for record in records:
            history.append({
                'software_name': record['software_name'],
                'points_consumed': record['points_consumed'],
                'timestamp': record['timestamp'],
                'ip_address': record['ip_address']
            })
        
        return jsonify({
            'success': True,
            'history': history
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': str(e)
        })
