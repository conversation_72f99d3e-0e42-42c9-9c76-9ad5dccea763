{% extends "base.html" %}

{% block title %}消费记录 - 点卡平台{% endblock %}

{% block content %}
<div class="card">
    <h2>消费记录</h2>
    
    {% if records.items %}
    <table class="table">
        <thead>
            <tr>
                <th>记录ID</th>
                <th>软件名称</th>
                <th>消费点数</th>
                <th>消费时间</th>
                <th>IP地址</th>
            </tr>
        </thead>
        <tbody>
            {% for record in records.items %}
            <tr>
                <td>#{{ record.id }}</td>
                <td>{{ record.software.name }}</td>
                <td>{{ record.points_consumed }}</td>
                <td>{{ record.timestamp.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                <td>{{ record.ip_address or '未知' }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    
    <!-- 分页 -->
    {% if records.pages > 1 %}
    <div class="pagination">
        {% if records.has_prev %}
            <a href="{{ url_for('user.consumption_records', page=records.prev_num) }}">&laquo; 上一页</a>
        {% endif %}
        
        {% for page_num in records.iter_pages() %}
            {% if page_num %}
                {% if page_num != records.page %}
                    <a href="{{ url_for('user.consumption_records', page=page_num) }}">{{ page_num }}</a>
                {% else %}
                    <span class="current">{{ page_num }}</span>
                {% endif %}
            {% else %}
                <span>...</span>
            {% endif %}
        {% endfor %}
        
        {% if records.has_next %}
            <a href="{{ url_for('user.consumption_records', page=records.next_num) }}">下一页 &raquo;</a>
        {% endif %}
    </div>
    {% endif %}
    
    {% else %}
    <div style="text-align: center; padding: 2rem; color: #666;">
        <p>暂无消费记录</p>
        <a href="{{ url_for('user.dashboard') }}" class="btn btn-primary">返回用户中心</a>
    </div>
    {% endif %}
</div>

<div style="text-align: center; margin-top: 1rem;">
    <a href="{{ url_for('user.dashboard') }}" class="btn btn-secondary">返回用户中心</a>
</div>
{% endblock %}
