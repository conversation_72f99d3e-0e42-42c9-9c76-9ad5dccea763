#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
修复用户最后登录时间字段问题
"""

import sys
import os
import sqlite3
from datetime import datetime
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def check_column_exists(cursor, table_name, column_name):
    """检查列是否存在"""
    cursor.execute(f"PRAGMA table_info({table_name})")
    columns = [column[1] for column in cursor.fetchall()]
    return column_name in columns

def fix_last_login_field():
    """修复最后登录时间字段"""
    print("🔧 修复用户最后登录时间字段...")
    
    db_path = "point_card_platform.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查当前表结构
        cursor.execute("PRAGMA table_info(user)")
        columns = [column[1] for column in cursor.fetchall()]
        print(f"   当前用户表列: {columns}")
        
        has_last_login = 'last_login' in columns
        
        if not has_last_login:
            print("   添加 last_login 列...")
            cursor.execute("ALTER TABLE user ADD COLUMN last_login TEXT")
            print("   ✅ last_login 列添加成功")
        else:
            print("   ✅ last_login 列已存在")
        
        # 检查是否有用户的 last_login 为空
        cursor.execute("SELECT COUNT(*) FROM user WHERE last_login IS NULL OR last_login = ''")
        null_count = cursor.fetchone()[0]
        
        if null_count > 0:
            print(f"   发现 {null_count} 个用户的最后登录时间为空")
            
            # 为这些用户设置一个默认的最后登录时间（注册时间）
            cursor.execute("""
                UPDATE user 
                SET last_login = created_at 
                WHERE (last_login IS NULL OR last_login = '') 
                AND created_at IS NOT NULL
            """)
            
            # 对于没有 created_at 的用户，设置为当前时间
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            cursor.execute("""
                UPDATE user 
                SET last_login = ? 
                WHERE (last_login IS NULL OR last_login = '') 
                AND (created_at IS NULL OR created_at = '')
            """, (current_time,))
            
            print("   ✅ 已为空值用户设置默认最后登录时间")
        
        conn.commit()
        conn.close()
        
        print("   ✅ 用户最后登录时间字段修复完成")
        return True
        
    except Exception as e:
        print(f"   ❌ 修复用户表失败: {e}")
        return False

def test_user_queries():
    """测试用户查询"""
    print("🧪 测试用户查询...")
    
    db_path = "point_card_platform.db"
    
    try:
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # 测试查询用户列表
        cursor.execute("""
            SELECT id, username, email, is_admin, is_super_admin, points, created_at, last_login 
            FROM user LIMIT 3
        """)
        results = cursor.fetchall()
        
        if results:
            for user in results:
                user_dict = dict(user)
                print(f"   测试用户: {user_dict['username']}")
                print(f"     最后登录: {user_dict.get('last_login', 'NULL')}")
                
                # 检查是否有 last_login 字段
                if 'last_login' in user_dict:
                    print("     ✅ last_login 字段正常")
                else:
                    print("     ❌ last_login 字段缺失")
                    return False
        else:
            print("   ℹ️  数据库中暂无用户数据")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"   ❌ 测试查询失败: {e}")
        return False

def update_admin_last_login():
    """更新管理员账户的最后登录时间"""
    print("🔧 更新管理员账户最后登录时间...")
    
    db_path = "point_card_platform.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 为管理员账户设置最近的登录时间
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        cursor.execute("""
            UPDATE user 
            SET last_login = ? 
            WHERE username IN ('admin', 'superadmin', 'testuser')
        """, (current_time,))
        
        updated_count = cursor.rowcount
        conn.commit()
        conn.close()
        
        print(f"   ✅ 已更新 {updated_count} 个管理员账户的最后登录时间")
        return True
        
    except Exception as e:
        print(f"   ❌ 更新管理员登录时间失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始修复用户最后登录时间问题")
    print("=" * 60)
    
    # 1. 修复用户表结构
    if not fix_last_login_field():
        print("❌ 用户表结构修复失败")
        return False
    
    # 2. 更新管理员登录时间
    if not update_admin_last_login():
        print("❌ 管理员登录时间更新失败")
        return False
    
    # 3. 测试查询
    if not test_user_queries():
        print("❌ 查询测试失败")
        return False
    
    print("\n🎉 用户最后登录时间修复完成！")
    print("现在用户登录时会自动更新最后登录时间。")
    print("管理员界面的用户管理页面将正确显示最后登录时间。")
    return True

if __name__ == "__main__":
    if main():
        print("\n✅ 修复成功！请重启应用程序以使更改生效。")
    else:
        print("\n❌ 修复失败！请检查错误信息。")
        sys.exit(1)
