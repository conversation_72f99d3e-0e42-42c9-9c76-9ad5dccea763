#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目清理脚本 - 移除旧文件和重复文件
"""

import os
import shutil
import glob

def cleanup_old_files():
    """清理旧的测试和调试文件"""
    print("🧹 清理项目文件")
    print("=" * 50)
    
    # 要删除的文件列表
    files_to_remove = [
        "debug_api_login.py",
        "debug_login.py", 
        "ensure_admin_user.py",
        "fix_admin_password.py",
        "quick_test.py",
        "test_api.py",
        "test_api.sh",
        "test_complete_api.py",
        "test_login_api.py",
        "deploy.sh",
        "stop_production.sh"
    ]
    
    removed_count = 0
    
    for file_path in files_to_remove:
        if os.path.exists(file_path):
            try:
                os.remove(file_path)
                print(f"   ✅ 删除: {file_path}")
                removed_count += 1
            except Exception as e:
                print(f"   ❌ 删除失败 {file_path}: {e}")
        else:
            print(f"   ⚪ 不存在: {file_path}")
    
    # 清理备份文件
    backup_files = glob.glob("*.backup_*")
    for backup_file in backup_files:
        if backup_file != "point_card_platform.db.backup_20250701_210258":  # 保留一个备份
            try:
                os.remove(backup_file)
                print(f"   ✅ 删除备份: {backup_file}")
                removed_count += 1
            except Exception as e:
                print(f"   ❌ 删除备份失败 {backup_file}: {e}")
    
    # 清理缓存目录
    cache_dirs = ["__pycache__", "routes/__pycache__"]
    for cache_dir in cache_dirs:
        if os.path.exists(cache_dir):
            try:
                shutil.rmtree(cache_dir)
                print(f"   ✅ 删除缓存: {cache_dir}")
                removed_count += 1
            except Exception as e:
                print(f"   ❌ 删除缓存失败 {cache_dir}: {e}")
    
    print(f"\n✅ 清理完成，共删除 {removed_count} 个文件/目录")

def create_gitignore():
    """创建.gitignore文件"""
    print("\n📝 创建.gitignore文件")
    print("=" * 50)
    
    gitignore_content = """# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
venv/
env/
ENV/

# Database
*.db
*.sqlite3
point_card_platform.db.backup_*

# Logs
logs/
*.log

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Application specific
instance/
gunicorn.pid
*.pid

# Temporary files
*.tmp
*.temp
"""
    
    try:
        with open(".gitignore", "w", encoding="utf-8") as f:
            f.write(gitignore_content)
        print("   ✅ .gitignore 创建成功")
    except Exception as e:
        print(f"   ❌ .gitignore 创建失败: {e}")

def create_license():
    """创建LICENSE文件"""
    print("\n📄 创建LICENSE文件")
    print("=" * 50)
    
    license_content = """MIT License

Copyright (c) 2025 Point Card Platform

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
"""
    
    try:
        with open("LICENSE", "w", encoding="utf-8") as f:
            f.write(license_content)
        print("   ✅ LICENSE 创建成功")
    except Exception as e:
        print(f"   ❌ LICENSE 创建失败: {e}")

def show_project_structure():
    """显示整理后的项目结构"""
    print("\n📁 项目结构")
    print("=" * 50)
    
    def print_tree(directory, prefix="", max_depth=3, current_depth=0):
        if current_depth >= max_depth:
            return
            
        items = []
        try:
            for item in os.listdir(directory):
                if not item.startswith('.') and item != '__pycache__':
                    items.append(item)
        except PermissionError:
            return
            
        items.sort()
        
        for i, item in enumerate(items):
            path = os.path.join(directory, item)
            is_last = i == len(items) - 1
            
            current_prefix = "└── " if is_last else "├── "
            print(f"{prefix}{current_prefix}{item}")
            
            if os.path.isdir(path) and current_depth < max_depth - 1:
                extension = "    " if is_last else "│   "
                print_tree(path, prefix + extension, max_depth, current_depth + 1)
    
    print_tree(".")

def main():
    """主函数"""
    print("🚀 点卡平台项目整理")
    print("=" * 60)
    
    cleanup_old_files()
    create_gitignore()
    create_license()
    show_project_structure()
    
    print("\n" + "=" * 60)
    print("🎉 项目整理完成!")
    print("\n📋 下一步:")
    print("   1. 检查项目结构是否正确")
    print("   2. 运行测试: python3 tests/test_api_complete.py")
    print("   3. 启动应用: ./scripts/start_production.sh")
    print("   4. 查看文档: docs/API_REFERENCE.md")

if __name__ == "__main__":
    main()
