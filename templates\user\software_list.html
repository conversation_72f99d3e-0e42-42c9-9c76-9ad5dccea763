{% extends "base.html" %}

{% block title %}软件列表 - 点卡平台{% endblock %}

{% block content %}
<style>
.software-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    border-radius: 15px;
    margin-bottom: 2rem;
    text-align: center;
}

.software-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin: 2rem 0;
}

.software-card {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 10px 40px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border: 1px solid rgba(102, 126, 234, 0.1);
    position: relative;
    overflow: hidden;
}

.software-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
}

.software-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 60px rgba(0,0,0,0.15);
}

.software-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    margin-bottom: 1.5rem;
}

.software-name {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.software-description {
    color: #666;
    line-height: 1.6;
    margin-bottom: 1.5rem;
    min-height: 3rem;
}

.software-points {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    padding: 0.8rem 1.5rem;
    border-radius: 25px;
    font-weight: 600;
    text-align: center;
    margin-bottom: 1rem;
}

.software-status {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    color: #27ae60;
    font-weight: 500;
}

.user-balance {
    background: linear-gradient(135deg, #4834d4, #686de0);
    color: white;
    padding: 1.5rem;
    border-radius: 15px;
    text-align: center;
    margin-bottom: 2rem;
}

.balance-amount {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 0.5rem;
}

.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: #666;
}

.empty-state h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #999;
}

.quick-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
    flex-wrap: wrap;
}
</style>

<div class="software-header">
    <h1>🔌 可用软件</h1>
    <p>查看平台上所有可用的软件及其扣费标准</p>
</div>

<div class="user-balance">
    <div class="balance-amount">{{ current_user.points }}</div>
    <p style="margin: 0; opacity: 0.9;">当前可用点数</p>
</div>

{% if software_list %}
<div class="software-grid">
    {% for software in software_list %}
    <div class="software-card">
        <div class="software-icon">
            🔧
        </div>
        
        <h3 class="software-name">{{ software.name }}</h3>
        
        <p class="software-description">
            {{ software.description or '暂无描述' }}
        </p>
        
        <div class="software-points">
            每次使用扣费: {{ software.points_per_use }} 点
        </div>
        
        <div class="software-status">
            <span style="color: #27ae60;">●</span>
            <span>服务正常</span>
        </div>
        
        {% if current_user.points >= software.points_per_use %}
        <div style="margin-top: 1rem; padding: 0.8rem; background: #d4edda; color: #155724; border-radius: 10px; text-align: center; font-weight: 500;">
            ✅ 点数充足，可以使用
        </div>
        {% else %}
        <div style="margin-top: 1rem; padding: 0.8rem; background: #f8d7da; color: #721c24; border-radius: 10px; text-align: center; font-weight: 500;">
            ❌ 点数不足，需要充值
        </div>
        {% endif %}
    </div>
    {% endfor %}
</div>

<div class="card" style="margin-top: 2rem; background: #f8f9fa; border: none;">
    <h3 style="color: #2c3e50; margin-bottom: 1rem;">💡 使用说明</h3>
    <ul style="color: #666; line-height: 1.8;">
        <li>每个软件都有独立的扣费标准</li>
        <li>使用软件前请确保账户有足够的点数</li>
        <li>软件开发者会通过API接口自动扣除相应点数</li>
        <li>所有消费记录都可以在"消费记录"页面查看</li>
        <li>如需充值，请前往"充值"页面使用卡密</li>
    </ul>
</div>

{% else %}
<div class="empty-state">
    <h3>暂无可用软件</h3>
    <p>管理员还没有添加任何软件到平台</p>
    <div style="font-size: 4rem; margin: 2rem 0; opacity: 0.3;">🔌</div>
</div>
{% endif %}

<div class="quick-actions">
    <a href="{{ url_for('user.dashboard') }}" class="btn btn-secondary">返回用户中心</a>
    <a href="{{ url_for('user.recharge') }}" class="btn btn-primary">充值点数</a>
    <a href="{{ url_for('user.history') }}" class="btn btn-info">消费记录</a>
</div>
{% endblock %}
