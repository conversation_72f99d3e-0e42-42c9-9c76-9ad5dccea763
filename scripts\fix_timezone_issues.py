#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
修复时区问题
统一所有时间记录为本地时间格式：%Y-%m-%d %H:%M:%S
"""

import sys
import os
import sqlite3
from datetime import datetime
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def check_time_formats():
    """检查数据库中的时间格式"""
    print("🔍 检查数据库中的时间格式...")
    
    db_path = "point_card_platform.db"
    
    try:
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # 检查卡密表的时间格式
        print("\n📋 卡密表时间格式:")
        cursor.execute("""
            SELECT card_code, created_at, used_at 
            FROM card 
            WHERE used_at IS NOT NULL 
            ORDER BY used_at DESC 
            LIMIT 5
        """)
        cards = cursor.fetchall()
        
        for card in cards:
            print(f"   卡密: {card['card_code']}")
            print(f"     创建时间: {card['created_at']}")
            print(f"     使用时间: {card['used_at']}")
            print()
        
        # 检查消费记录表的时间格式
        print("📋 消费记录时间格式:")
        cursor.execute("""
            SELECT id, timestamp, points_consumed 
            FROM consumption_record 
            ORDER BY timestamp DESC 
            LIMIT 5
        """)
        records = cursor.fetchall()
        
        for record in records:
            print(f"   记录ID: {record['id']}")
            print(f"     时间: {record['timestamp']}")
            print(f"     点数: {record['points_consumed']}")
            print()
        
        # 检查用户表的时间格式
        print("📋 用户表时间格式:")
        cursor.execute("""
            SELECT username, created_at, last_login 
            FROM user 
            WHERE last_login IS NOT NULL 
            ORDER BY last_login DESC 
            LIMIT 5
        """)
        users = cursor.fetchall()
        
        for user in users:
            print(f"   用户: {user['username']}")
            print(f"     注册时间: {user['created_at']}")
            print(f"     最后登录: {user['last_login']}")
            print()
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"   ❌ 检查时间格式失败: {e}")
        return False

def fix_card_timestamps():
    """修复卡密时间戳"""
    print("🔧 修复卡密时间戳...")
    
    db_path = "point_card_platform.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 查找需要修复的时间戳
        cursor.execute("""
            SELECT id, used_at 
            FROM card 
            WHERE used_at IS NOT NULL 
            AND (used_at LIKE '%T%' OR used_at LIKE '%.%' OR LENGTH(used_at) != 19)
        """)
        cards_to_fix = cursor.fetchall()
        
        if cards_to_fix:
            print(f"   发现 {len(cards_to_fix)} 个需要修复的卡密时间戳")
            
            for card in cards_to_fix:
                card_id, used_at = card
                
                try:
                    # 尝试解析各种时间格式
                    if 'T' in used_at:
                        # ISO格式: 2025-07-03T03:27:00.123456
                        dt = datetime.fromisoformat(used_at.replace('T', ' ').split('.')[0])
                    elif '.' in used_at:
                        # 带微秒: 2025-07-03 03:27:00.123456
                        dt = datetime.strptime(used_at.split('.')[0], '%Y-%m-%d %H:%M:%S')
                    else:
                        # 标准格式，无需修复
                        continue
                    
                    # 转换为标准格式
                    standard_time = dt.strftime('%Y-%m-%d %H:%M:%S')
                    
                    cursor.execute("""
                        UPDATE card SET used_at = ? WHERE id = ?
                    """, (standard_time, card_id))
                    
                    print(f"     修复卡密ID {card_id}: {used_at} -> {standard_time}")
                    
                except Exception as e:
                    print(f"     ❌ 修复卡密ID {card_id} 失败: {e}")
            
            conn.commit()
            print("   ✅ 卡密时间戳修复完成")
        else:
            print("   ✅ 卡密时间戳格式正常，无需修复")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"   ❌ 修复卡密时间戳失败: {e}")
        return False

def fix_consumption_timestamps():
    """修复消费记录时间戳"""
    print("🔧 修复消费记录时间戳...")
    
    db_path = "point_card_platform.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 查找需要修复的时间戳
        cursor.execute("""
            SELECT id, timestamp 
            FROM consumption_record 
            WHERE timestamp IS NOT NULL 
            AND (timestamp LIKE '%T%' OR timestamp LIKE '%.%' OR LENGTH(timestamp) != 19)
        """)
        records_to_fix = cursor.fetchall()
        
        if records_to_fix:
            print(f"   发现 {len(records_to_fix)} 个需要修复的消费记录时间戳")
            
            for record in records_to_fix:
                record_id, timestamp = record
                
                try:
                    # 尝试解析各种时间格式
                    if 'T' in timestamp:
                        # ISO格式
                        dt = datetime.fromisoformat(timestamp.replace('T', ' ').split('.')[0])
                    elif '.' in timestamp:
                        # 带微秒
                        dt = datetime.strptime(timestamp.split('.')[0], '%Y-%m-%d %H:%M:%S')
                    else:
                        # 标准格式，无需修复
                        continue
                    
                    # 转换为标准格式
                    standard_time = dt.strftime('%Y-%m-%d %H:%M:%S')
                    
                    cursor.execute("""
                        UPDATE consumption_record SET timestamp = ? WHERE id = ?
                    """, (standard_time, record_id))
                    
                    print(f"     修复记录ID {record_id}: {timestamp} -> {standard_time}")
                    
                except Exception as e:
                    print(f"     ❌ 修复记录ID {record_id} 失败: {e}")
            
            conn.commit()
            print("   ✅ 消费记录时间戳修复完成")
        else:
            print("   ✅ 消费记录时间戳格式正常，无需修复")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"   ❌ 修复消费记录时间戳失败: {e}")
        return False

def fix_api_log_timestamps():
    """修复API日志时间戳"""
    print("🔧 修复API日志时间戳...")
    
    db_path = "point_card_platform.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查api_logs表是否存在
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='api_logs'
        """)
        
        if not cursor.fetchone():
            print("   ℹ️  API日志表不存在，跳过修复")
            conn.close()
            return True
        
        # 查找需要修复的时间戳
        cursor.execute("""
            SELECT id, timestamp 
            FROM api_logs 
            WHERE timestamp IS NOT NULL 
            AND (timestamp LIKE '%T%' OR timestamp LIKE '%.%' OR LENGTH(timestamp) != 19)
        """)
        logs_to_fix = cursor.fetchall()
        
        if logs_to_fix:
            print(f"   发现 {len(logs_to_fix)} 个需要修复的API日志时间戳")
            
            for log in logs_to_fix:
                log_id, timestamp = log
                
                try:
                    # 尝试解析各种时间格式
                    if 'T' in timestamp:
                        # ISO格式
                        dt = datetime.fromisoformat(timestamp.replace('T', ' ').split('.')[0])
                    elif '.' in timestamp:
                        # 带微秒
                        dt = datetime.strptime(timestamp.split('.')[0], '%Y-%m-%d %H:%M:%S')
                    else:
                        # 标准格式，无需修复
                        continue
                    
                    # 转换为标准格式
                    standard_time = dt.strftime('%Y-%m-%d %H:%M:%S')
                    
                    cursor.execute("""
                        UPDATE api_logs SET timestamp = ? WHERE id = ?
                    """, (standard_time, log_id))
                    
                    print(f"     修复日志ID {log_id}: {timestamp} -> {standard_time}")
                    
                except Exception as e:
                    print(f"     ❌ 修复日志ID {log_id} 失败: {e}")
            
            conn.commit()
            print("   ✅ API日志时间戳修复完成")
        else:
            print("   ✅ API日志时间戳格式正常，无需修复")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"   ❌ 修复API日志时间戳失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始修复时区问题")
    print("=" * 60)
    
    # 1. 检查当前时间格式
    if not check_time_formats():
        print("❌ 时间格式检查失败")
        return False
    
    # 2. 修复卡密时间戳
    if not fix_card_timestamps():
        print("❌ 卡密时间戳修复失败")
        return False
    
    # 3. 修复消费记录时间戳
    if not fix_consumption_timestamps():
        print("❌ 消费记录时间戳修复失败")
        return False
    
    # 4. 修复API日志时间戳
    if not fix_api_log_timestamps():
        print("❌ API日志时间戳修复失败")
        return False
    
    print("\n🎉 时区问题修复完成！")
    print("所有时间记录现在都使用统一的本地时间格式。")
    print("重启应用程序后，新的时间记录将使用正确的格式。")
    return True

if __name__ == "__main__":
    if main():
        print("\n✅ 修复成功！请重启应用程序以使更改生效。")
    else:
        print("\n❌ 修复失败！请检查错误信息。")
        sys.exit(1)
