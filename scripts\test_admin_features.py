#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试管理员功能
1. 测试超级管理员软件编辑功能
2. 测试统计报表时间格式修复
"""

import sys
import os
import sqlite3
from datetime import datetime, date

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_software_update_function():
    """测试软件更新功能"""
    print("🔧 测试软件更新功能...")
    
    try:
        from bypass_sqlalchemy import DatabaseManager
        
        db_manager = DatabaseManager()
        
        # 获取第一个软件
        software_list = db_manager.get_all_software()
        if not software_list:
            print("   ❌ 没有找到软件，无法测试")
            return False
        
        software = software_list[0]
        original_points = software['points_per_use']
        software_id = software['id']
        
        print(f"   📋 测试软件: {software['name']} (ID: {software_id})")
        print(f"   📋 原始扣费点数: {original_points}")
        
        # 测试更新软件信息
        new_points = original_points + 1
        success = db_manager.update_software(
            software_id=software_id,
            name=software['name'],
            description=software.get('description', '') + ' [已测试]',
            points_per_use=new_points
        )
        
        if success:
            print(f"   ✅ 软件更新成功")
            
            # 验证更新结果
            updated_software = db_manager.get_software_by_id(software_id)
            if updated_software and updated_software['points_per_use'] == new_points:
                print(f"   ✅ 扣费点数更新验证成功: {original_points} -> {new_points}")
                
                # 恢复原始值
                db_manager.update_software(
                    software_id=software_id,
                    name=software['name'],
                    description=software.get('description', ''),
                    points_per_use=original_points
                )
                print(f"   ✅ 已恢复原始扣费点数: {original_points}")
                return True
            else:
                print(f"   ❌ 扣费点数更新验证失败")
                return False
        else:
            print(f"   ❌ 软件更新失败")
            return False
            
    except Exception as e:
        print(f"   ❌ 测试软件更新功能失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_statistics_time_format():
    """测试统计报表时间格式"""
    print("📊 测试统计报表时间格式...")
    
    try:
        from bypass_sqlalchemy import DatabaseManager
        
        db_manager = DatabaseManager()
        
        # 测试每日消费统计
        daily_stats = db_manager.get_daily_consumption_stats(7)
        
        print(f"   📋 获取到 {len(daily_stats)} 天的统计数据")
        
        for i, stat in enumerate(daily_stats[:3]):  # 只检查前3条
            print(f"   📋 第{i+1}条: 日期={stat.get('date')}, 类型={type(stat.get('date'))}, 消费={stat.get('total_consumed')}")
            
            # 检查日期对象是否可以调用 strftime
            if stat.get('date'):
                try:
                    if hasattr(stat['date'], 'strftime'):
                        formatted_date = stat['date'].strftime('%m/%d')
                        print(f"      ✅ 日期格式化成功: {formatted_date}")
                    else:
                        print(f"      ❌ 日期对象没有 strftime 方法")
                        return False
                except Exception as e:
                    print(f"      ❌ 日期格式化失败: {e}")
                    return False
        
        print("   ✅ 统计报表时间格式测试通过")
        return True
        
    except Exception as e:
        print(f"   ❌ 测试统计报表时间格式失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_super_admin_permissions():
    """测试超级管理员权限"""
    print("👑 测试超级管理员权限...")
    
    try:
        conn = sqlite3.connect('dianka.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # 检查超级管理员用户
        cursor.execute("SELECT username, is_super_admin FROM user WHERE is_super_admin = 1")
        super_admins = cursor.fetchall()
        
        print("   📋 超级管理员用户:")
        for admin in super_admins:
            print(f"      {admin['username']} (超级管理员: {admin['is_super_admin']})")
        
        if len(super_admins) > 0:
            print("   ✅ 超级管理员权限检查通过")
            print("   ℹ️  超级管理员可以:")
            print("      - 编辑软件扣费点数")
            print("      - 修改软件名称和描述")
            print("      - 访问系统设置页面")
            print("      - 创建和管理普通管理员")
            conn.close()
            return True
        else:
            print("   ❌ 未找到超级管理员用户")
            conn.close()
            return False
            
    except Exception as e:
        print(f"   ❌ 检查超级管理员权限失败: {e}")
        return False

def create_test_consumption_data():
    """创建测试消费数据（用于统计报表测试）"""
    print("📝 创建测试消费数据...")
    
    try:
        conn = sqlite3.connect('dianka.db')
        cursor = conn.cursor()
        
        # 检查是否已有消费记录
        cursor.execute("SELECT COUNT(*) FROM consumption_record")
        count = cursor.fetchone()[0]
        
        if count > 0:
            print(f"   ℹ️  已有 {count} 条消费记录，跳过创建测试数据")
            conn.close()
            return True
        
        # 获取用户和软件ID
        cursor.execute("SELECT id FROM user WHERE username = 'admin'")
        user_result = cursor.fetchone()
        if not user_result:
            print("   ❌ 未找到管理员用户")
            conn.close()
            return False
        user_id = user_result[0]
        
        cursor.execute("SELECT id FROM software LIMIT 1")
        software_result = cursor.fetchone()
        if not software_result:
            print("   ❌ 未找到软件")
            conn.close()
            return False
        software_id = software_result[0]
        
        # 创建测试消费记录
        test_records = []
        for i in range(5):
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            test_records.append((user_id, software_id, 10, '127.0.0.1', 'Test Agent', timestamp))
        
        cursor.executemany("""
            INSERT INTO consumption_record (user_id, software_id, points_consumed, ip_address, user_agent, timestamp)
            VALUES (?, ?, ?, ?, ?, ?)
        """, test_records)
        
        conn.commit()
        conn.close()
        
        print(f"   ✅ 创建了 {len(test_records)} 条测试消费记录")
        return True
        
    except Exception as e:
        print(f"   ❌ 创建测试消费数据失败: {e}")
        return False

def show_usage_instructions():
    """显示使用说明"""
    print("\n📖 功能使用说明")
    print("=" * 60)
    print("🔧 软件编辑功能:")
    print("   1. 使用超级管理员账号登录 (superadmin / super123)")
    print("   2. 访问 /admin/software 页面")
    print("   3. 点击软件列表中的 '编辑' 按钮")
    print("   4. 修改扣费点数、名称或描述")
    print("   5. 点击 '保存修改' 按钮")
    print("\n📊 统计报表功能:")
    print("   1. 使用管理员或超级管理员账号登录")
    print("   2. 访问 /admin/statistics 页面")
    print("   3. 查看软件消费统计和每日消费趋势")
    print("   4. 时间格式错误已修复，不会再出现 strftime 错误")

def main():
    """主函数"""
    print("🚀 开始测试管理员功能")
    print("=" * 60)
    
    results = []
    
    # 1. 创建测试数据
    results.append(("创建测试数据", create_test_consumption_data()))
    
    # 2. 测试软件更新功能
    results.append(("软件更新功能", test_software_update_function()))
    
    # 3. 测试统计报表时间格式
    results.append(("统计报表时间格式", test_statistics_time_format()))
    
    # 4. 测试超级管理员权限
    results.append(("超级管理员权限", test_super_admin_permissions()))
    
    # 输出结果
    print("\n📊 测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 成功" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有功能测试通过！")
        show_usage_instructions()
        return True
    else:
        print("⚠️  部分测试失败，请检查系统配置。")
        return False

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n✅ 功能测试完成！")
            sys.exit(0)
        else:
            print("\n❌ 功能测试失败！")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⚠️  测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
