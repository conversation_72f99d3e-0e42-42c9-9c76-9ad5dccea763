{% extends "base.html" %}

{% block title %}管理后台 - 点卡平台{% endblock %}

{% block content %}
<h1>管理后台</h1>

<div class="stats-grid">
    <div class="stat-card">
        <h3>{{ stats.total_users }}</h3>
        <p>总用户数</p>
    </div>
    <div class="stat-card">
        <h3>{{ stats.total_cards }}</h3>
        <p>总卡密数</p>
    </div>
    <div class="stat-card">
        <h3>{{ stats.used_cards }}</h3>
        <p>已使用卡密</p>
    </div>
    <div class="stat-card">
        <h3>{{ stats.unused_cards }}</h3>
        <p>未使用卡密</p>
    </div>
    <div class="stat-card">
        <h3>{{ stats.total_software }}</h3>
        <p>软件数量</p>
    </div>
    <div class="stat-card">
        <h3>{{ stats.today_consumption }}</h3>
        <p>今日消费点数</p>
    </div>
</div>

<div class="card">
    <h2>快速操作</h2>
    <div style="display: flex; gap: 1rem; flex-wrap: wrap;">
        <a href="{{ url_for('admin.create_cards') }}" class="btn btn-primary">创建卡密</a>
        <a href="{{ url_for('admin.software') }}" class="btn btn-success">软件管理</a>
        <a href="{{ url_for('admin.users') }}" class="btn btn-secondary">用户管理</a>
        <a href="{{ url_for('admin.cards') }}" class="btn btn-warning">卡密管理</a>
        <a href="{{ url_for('admin.statistics') }}" class="btn btn-info">统计报表</a>
    </div>
</div>

{% if recent_records %}
<div class="card">
    <h2>最近消费记录</h2>
    <table class="table">
        <thead>
            <tr>
                <th>用户</th>
                <th>软件</th>
                <th>消费点数</th>
                <th>时间</th>
                <th>IP地址</th>
            </tr>
        </thead>
        <tbody>
            {% for record in recent_records %}
            <tr>
                <td>{{ record.user.username }}</td>
                <td>{{ record.software.name }}</td>
                <td>{{ record.points_consumed }}</td>
                <td>{{ record.timestamp.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                <td>{{ record.ip_address or '未知' }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    <div style="text-align: center; margin-top: 1rem;">
        <a href="{{ url_for('admin.consumption_records') }}" class="btn btn-secondary">查看全部记录</a>
    </div>
</div>
{% endif %}
{% endblock %}
