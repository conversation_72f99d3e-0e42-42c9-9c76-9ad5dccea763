#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
修复卡密价格字段问题
数据库中使用的是 currency_amount，但代码期望的是 price
"""

import sys
import os
import sqlite3
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def check_column_exists(cursor, table_name, column_name):
    """检查列是否存在"""
    cursor.execute(f"PRAGMA table_info({table_name})")
    columns = [column[1] for column in cursor.fetchall()]
    return column_name in columns

def fix_card_table():
    """修复卡密表结构"""
    print("🔧 修复卡密表结构...")
    
    db_path = "point_card_platform.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查当前表结构
        cursor.execute("PRAGMA table_info(card)")
        columns = [column[1] for column in cursor.fetchall()]
        print(f"   当前卡密表列: {columns}")
        
        has_price = 'price' in columns
        has_currency_amount = 'currency_amount' in columns
        
        if has_currency_amount and not has_price:
            print("   添加 price 列并从 currency_amount 复制数据...")
            # 添加 price 列
            cursor.execute("ALTER TABLE card ADD COLUMN price REAL")
            # 从 currency_amount 复制数据到 price
            cursor.execute("UPDATE card SET price = currency_amount")
            
        elif not has_price and not has_currency_amount:
            print("   添加 price 列...")
            cursor.execute("ALTER TABLE card ADD COLUMN price REAL DEFAULT 0")
            
        elif has_price and has_currency_amount:
            print("   两个字段都存在，确保数据一致...")
            # 确保 price 字段有数据
            cursor.execute("UPDATE card SET price = currency_amount WHERE price IS NULL OR price = 0")
        
        conn.commit()
        conn.close()
        
        print("   ✅ 卡密表结构修复完成")
        return True
        
    except Exception as e:
        print(f"   ❌ 修复卡密表失败: {e}")
        return False

def update_database_manager():
    """更新数据库管理器中的查询"""
    print("🔧 更新数据库查询...")
    
    try:
        # 这里我们需要修复 bypass_sqlalchemy.py 中的查询
        print("   需要手动更新 bypass_sqlalchemy.py 中的查询语句")
        print("   将 currency_amount 替换为 price")
        return True
        
    except Exception as e:
        print(f"   ❌ 更新查询失败: {e}")
        return False

def test_card_queries():
    """测试卡密查询"""
    print("🧪 测试卡密查询...")
    
    db_path = "point_card_platform.db"
    
    try:
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # 测试查询卡密列表
        cursor.execute("SELECT id, card_code, points_value, price, is_used, created_at FROM card LIMIT 1")
        result = cursor.fetchone()
        
        if result:
            card = dict(result)
            print(f"   测试卡密: {card}")
            
            # 检查是否有 price 字段
            if 'price' in card and card['price'] is not None:
                print("   ✅ price 字段正常")
            else:
                print("   ❌ price 字段缺失或为空")
                return False
        else:
            print("   ℹ️  数据库中暂无卡密数据")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"   ❌ 测试查询失败: {e}")
        return False

def create_test_card():
    """创建测试卡密"""
    print("🧪 创建测试卡密...")
    
    db_path = "point_card_platform.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查是否已有测试卡密
        cursor.execute("SELECT COUNT(*) FROM card WHERE card_code = ?", ('TEST001',))
        if cursor.fetchone()[0] == 0:
            # 创建测试卡密
            cursor.execute("""
                INSERT INTO card (card_code, points_value, price, currency_amount, is_used, created_by, created_at)
                VALUES (?, ?, ?, ?, ?, ?, datetime('now'))
            """, ('TEST001', 100, 10.0, 10, 0, 1))
            
            cursor.execute("""
                INSERT INTO card (card_code, points_value, price, currency_amount, is_used, created_by, created_at)
                VALUES (?, ?, ?, ?, ?, ?, datetime('now'))
            """, ('TEST002', 500, 50.0, 50, 0, 1))
            
            conn.commit()
            print("   ✅ 测试卡密创建完成")
        else:
            print("   ℹ️  测试卡密已存在")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"   ❌ 创建测试卡密失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始修复卡密价格字段问题")
    print("=" * 60)
    
    # 1. 修复卡密表结构
    if not fix_card_table():
        print("❌ 卡密表结构修复失败")
        return False
    
    # 2. 创建测试卡密
    if not create_test_card():
        print("❌ 测试卡密创建失败")
        return False
    
    # 3. 测试查询
    if not test_card_queries():
        print("❌ 查询测试失败")
        return False
    
    print("\n🎉 卡密价格字段修复完成！")
    print("现在需要重启应用程序以使更改生效。")
    return True

if __name__ == "__main__":
    if main():
        print("\n✅ 修复成功！请重启应用程序。")
    else:
        print("\n❌ 修复失败！请检查错误信息。")
        sys.exit(1)
