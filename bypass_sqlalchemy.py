#!/usr/bin/env python3
"""
绕过Flask-SQLAlchemy问题的解决方案
"""

import os
import sqlite3
import hashlib
from contextlib import contextmanager
from datetime import datetime

class DatabaseManager:
    """直接使用SQLite的数据库管理器"""
    
    def __init__(self, db_path='point_card_platform.db'):
        self.db_path = db_path
        self._ensure_database_exists()

    def _parse_datetime(self, datetime_value):
        """解析时间值为 datetime 对象"""
        from datetime import datetime

        # 如果是 None，返回当前时间
        if datetime_value is None:
            return datetime.now()

        # 如果已经是 datetime 对象，直接返回
        if isinstance(datetime_value, datetime):
            return datetime_value

        # 如果是字符串，尝试解析
        if isinstance(datetime_value, str):
            # 跳过空字符串
            if not datetime_value.strip():
                return datetime.now()
            try:
                return datetime.strptime(datetime_value, '%Y-%m-%d %H:%M:%S')
            except ValueError:
                try:
                    return datetime.strptime(datetime_value, '%Y-%m-%d')
                except ValueError:
                    print(f"Warning: Cannot parse datetime string '{datetime_value}', using current time")
                    return datetime.now()

        # 如果是整数或浮点数，假设是 Unix 时间戳
        if isinstance(datetime_value, (int, float)):
            # 检查是否是合理的时间戳范围 (1970-2100)
            if 0 <= datetime_value <= 4102444800:  # 2100年的时间戳
                try:
                    return datetime.fromtimestamp(datetime_value)
                except (ValueError, OSError) as e:
                    print(f"Warning: Cannot parse timestamp {datetime_value}: {e}, using current time")
                    return datetime.now()
            else:
                print(f"Warning: Timestamp {datetime_value} out of range, using current time")
                return datetime.now()

        # 如果是其他类型，返回当前时间
        print(f"Warning: Unknown datetime type {type(datetime_value)} for value '{datetime_value}', using current time")
        return datetime.now()
    
    def _ensure_database_exists(self):
        """确保数据库和表存在"""
        import os
        if not os.path.exists(self.db_path):
            print(f"数据库文件不存在，正在创建: {self.db_path}")
            self._create_database()
        else:
            # 检查表是否存在
            try:
                conn = sqlite3.connect(self.db_path)
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='user'")
                if not cursor.fetchone():
                    print("数据库表不存在，正在创建...")
                    conn.close()
                    self._create_database()
                else:
                    conn.close()
            except Exception as e:
                print(f"检查数据库时出错: {e}")
                print("重新创建数据库...")
                self._create_database()

    @contextmanager
    def get_connection(self):
        """获取数据库连接"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # 使结果可以像字典一样访问
        try:
            yield conn
        finally:
            conn.close()
    
    def _create_database(self):
        """创建数据库表和初始数据"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            # 创建用户表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS user (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username VARCHAR(80) UNIQUE NOT NULL,
                    email VARCHAR(120) UNIQUE NOT NULL,
                    password_hash VARCHAR(255) NOT NULL,
                    points INTEGER DEFAULT 0,
                    is_admin BOOLEAN DEFAULT 0,
                    is_super_admin BOOLEAN DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # 创建卡密表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS card (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    card_code VARCHAR(50) UNIQUE NOT NULL,
                    points_value INTEGER NOT NULL,
                    currency_amount INTEGER NOT NULL,
                    is_used BOOLEAN DEFAULT 0,
                    used_by INTEGER,
                    used_at TIMESTAMP,
                    created_by INTEGER NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (used_by) REFERENCES user (id),
                    FOREIGN KEY (created_by) REFERENCES user (id)
                )
            ''')

            # 创建软件表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS software (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name VARCHAR(100) NOT NULL,
                    description TEXT,
                    api_key VARCHAR(255) UNIQUE NOT NULL,
                    points_per_use INTEGER DEFAULT 1,
                    is_active BOOLEAN DEFAULT 1,
                    created_by INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (created_by) REFERENCES user (id)
                )
            ''')

            # 创建消费记录表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS consumption_record (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    software_id INTEGER,
                    points_consumed INTEGER NOT NULL,
                    ip_address VARCHAR(45),
                    user_agent TEXT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES user (id),
                    FOREIGN KEY (software_id) REFERENCES software (id)
                )
            ''')

            # 创建API日志表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS api_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    api_key VARCHAR(255),
                    endpoint VARCHAR(100),
                    ip_address VARCHAR(45),
                    user_agent TEXT,
                    success BOOLEAN,
                    error_message TEXT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # 创建系统设置表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS system_settings (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    key VARCHAR(100) UNIQUE NOT NULL,
                    value TEXT,
                    description TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # 插入默认用户（如果不存在）
            cursor.execute("SELECT COUNT(*) FROM user")
            if cursor.fetchone()[0] == 0:
                users = [
                    ('superadmin', '<EMAIL>', self.hash_password('super123'), 1000, 1, 1),
                    ('admin', '<EMAIL>', self.hash_password('admin123'), 500, 1, 0),
                    ('testuser', '<EMAIL>', self.hash_password('test123'), 100, 0, 0)
                ]

                cursor.executemany('''
                    INSERT INTO user (username, email, password_hash, points, is_admin, is_super_admin)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', users)

                # 插入系统设置
                settings = [
                    ('points_per_yuan', '100', '每元人民币对应的点数'),
                    ('yuan_to_point_rate', '100', '人民币到点数汇率'),
                    ('site_name', '点卡平台', '网站名称'),
                    ('max_api_requests_per_minute', '60', '每分钟最大API请求数')
                ]

                cursor.executemany('''
                    INSERT INTO system_settings (key, value, description)
                    VALUES (?, ?, ?)
                ''', settings)

                # 插入测试软件
                cursor.execute('''
                    INSERT INTO software (name, api_key, points_per_use)
                    VALUES ('测试软件', 'test_api_key_123456', 1)
                ''')

                # 插入测试卡密
                test_cards = [
                    ('CARD001', 100, 1),
                    ('CARD002', 500, 5),
                    ('CARD003', 1000, 10)
                ]

                cursor.executemany('''
                    INSERT INTO card (card_code, points_value, currency_amount, created_by)
                    VALUES (?, ?, ?, 1)
                ''', test_cards)

                print("已创建默认用户和测试数据")

            conn.commit()
            print("数据库创建完成")

        except Exception as e:
            print(f"创建数据库时出错: {e}")
            conn.rollback()
            raise
        finally:
            conn.close()

    def hash_password(self, password):
        """密码哈希"""
        return hashlib.pbkdf2_hmac('sha256', password.encode('utf-8'), b'salt', 100000).hex()
    
    def check_password(self, password, hashed):
        """检查密码"""
        return self.hash_password(password) == hashed

    def verify_password(self, password, hashed):
        """验证密码（API使用）"""
        # 首先尝试SHA256哈希（旧版本兼容）
        import hashlib
        sha256_hash = hashlib.sha256(password.encode()).hexdigest()
        if sha256_hash == hashed:
            return True

        # 然后尝试PBKDF2哈希（新版本）
        return self.hash_password(password) == hashed
    
    def get_user_by_username(self, username):
        """根据用户名获取用户"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            # 检查表结构，确定是否有 last_login 字段
            cursor.execute("PRAGMA table_info(user)")
            columns = [column[1] for column in cursor.fetchall()]

            if 'last_login' in columns:
                cursor.execute("""
                    SELECT id, username, email, password_hash, is_admin, is_super_admin, points, created_at, last_login
                    FROM user WHERE username = ?
                """, (username,))
            else:
                cursor.execute("""
                    SELECT id, username, email, password_hash, is_admin, is_super_admin, points, created_at, NULL as last_login
                    FROM user WHERE username = ?
                """, (username,))

            row = cursor.fetchone()
            return dict(row) if row else None
    
    def get_user_by_id(self, user_id):
        """根据ID获取用户"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            # 检查表结构，确定是否有 last_login 字段
            cursor.execute("PRAGMA table_info(user)")
            columns = [column[1] for column in cursor.fetchall()]

            if 'last_login' in columns:
                cursor.execute("""
                    SELECT id, username, email, password_hash, is_admin, is_super_admin, points, created_at, last_login
                    FROM user WHERE id = ?
                """, (user_id,))
            else:
                cursor.execute("""
                    SELECT id, username, email, password_hash, is_admin, is_super_admin, points, created_at, NULL as last_login
                    FROM user WHERE id = ?
                """, (user_id,))

            row = cursor.fetchone()
            return dict(row) if row else None
    
    def authenticate_user(self, username, password):
        """验证用户"""
        user = self.get_user_by_username(username)
        if user and self.check_password(password, user['password_hash']):
            return user
        return None
    
    def update_user_points(self, user_id, points):
        """更新用户点数"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE user SET points = ? WHERE id = ?
            """, (points, user_id))
            conn.commit()
            return cursor.rowcount > 0
    
    def get_cards_by_creator(self, creator_id):
        """获取创建者的卡密"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT * FROM card WHERE created_by = ? ORDER BY created_at DESC
            """, (creator_id,))
            return cursor.fetchall()
    

    
    def use_card(self, card_code, user_id):
        """使用卡密"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # 检查卡密是否存在且未使用
            cursor.execute("""
                SELECT id, points_value, is_used FROM card 
                WHERE card_code = ? AND is_used = 0
            """, (card_code,))
            card = cursor.fetchone()
            
            if not card:
                return False, "卡密不存在或已使用"
            
            # 标记卡密为已使用
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            cursor.execute("""
                UPDATE card SET is_used = 1, used_by = ?, used_at = ?
                WHERE id = ?
            """, (user_id, current_time, card['id']))
            
            # 增加用户点数
            cursor.execute("""
                UPDATE user SET points = points + ? WHERE id = ?
            """, (card['points_value'], user_id))
            
            conn.commit()
            return True, f"成功充值 {card['points_value']} 点数"

    # 统计方法
    def get_user_count(self):
        """获取普通用户数量"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM user WHERE is_admin = 0")
            result = cursor.fetchone()
            return result[0] if result else 0



    def get_used_card_count(self):
        """获取已使用卡片数量"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM card WHERE is_used = 1")
            result = cursor.fetchone()
            return result[0] if result else 0

    def get_software_count(self):
        """获取软件数量"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM software")
            result = cursor.fetchone()
            return result[0] if result else 0

    def get_today_consumption(self, today):
        """获取今日消费总额"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            # 处理 date 对象和 datetime 对象
            if hasattr(today, 'strftime'):
                date_str = today.strftime('%Y-%m-%d')
            else:
                # 如果是 date 对象，转换为字符串
                date_str = str(today)

            cursor.execute("""
                SELECT COALESCE(SUM(points_consumed), 0) FROM consumption_record
                WHERE DATE(timestamp) = ?
            """, (date_str,))
            result = cursor.fetchone()
            return result[0] if result and result[0] else 0

    def get_recent_consumption_records(self, limit=10):
        """获取最近的消费记录"""
        from datetime import datetime
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT cr.id, cr.user_id, cr.software_id, cr.points_consumed, cr.timestamp,
                       cr.ip_address, cr.user_agent, u.username, s.name as software_name
                FROM consumption_record cr
                LEFT JOIN user u ON cr.user_id = u.id
                LEFT JOIN software s ON cr.software_id = s.id
                ORDER BY cr.timestamp DESC
                LIMIT ?
            """, (limit,))

            records = []
            for row in cursor.fetchall():
                record_dict = dict(row)
                # 创建嵌套对象结构以兼容模板
                record_dict['user'] = {'username': record_dict.get('username', '未知用户')}
                record_dict['software'] = {'name': record_dict.get('software_name', '未知软件')}

                # 处理时间戳
                timestamp_str = record_dict.get('timestamp', '')
                if timestamp_str:
                    try:
                        # 尝试解析时间戳
                        if isinstance(timestamp_str, str):
                            record_dict['timestamp'] = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S')
                        else:
                            record_dict['timestamp'] = timestamp_str
                    except:
                        # 如果解析失败，创建一个假的 datetime 对象
                        record_dict['timestamp'] = datetime.now()
                else:
                    record_dict['timestamp'] = datetime.now()

                records.append(record_dict)

            return records

    # 系统设置方法
    def get_system_setting(self, key, default_value=None):
        """获取系统设置"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            # 先尝试新的列名
            try:
                cursor.execute("SELECT value FROM system_settings WHERE key = ?", (key,))
                result = cursor.fetchone()
                return result['value'] if result else default_value
            except Exception:
                # 如果失败，尝试旧的列名
                try:
                    cursor.execute("SELECT setting_value FROM system_settings WHERE setting_key = ?", (key,))
                    result = cursor.fetchone()
                    return result['setting_value'] if result else default_value
                except Exception:
                    return default_value

    def set_system_setting(self, key, value, description=None):
        """设置系统设置"""
        from datetime import datetime
        with self.get_connection() as conn:
            cursor = conn.cursor()
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            # 先尝试新的列名
            try:
                # 检查是否已存在
                cursor.execute("SELECT id FROM system_settings WHERE key = ?", (key,))
                exists = cursor.fetchone()

                if exists:
                    # 更新现有设置
                    cursor.execute("""
                        UPDATE system_settings
                        SET value = ?, updated_at = ?, description = COALESCE(?, description)
                        WHERE key = ?
                    """, (value, current_time, description, key))
                else:
                    # 插入新设置
                    cursor.execute("""
                        INSERT INTO system_settings (key, value, description, created_at, updated_at)
                        VALUES (?, ?, ?, ?, ?)
                    """, (key, value, description or '', current_time, current_time))

                conn.commit()

            except Exception:
                # 如果失败，尝试旧的列名
                try:
                    # 检查是否已存在
                    cursor.execute("SELECT id FROM system_settings WHERE setting_key = ?", (key,))
                    exists = cursor.fetchone()

                    if exists:
                        # 更新现有设置
                        cursor.execute("""
                            UPDATE system_settings
                            SET setting_value = ?, description = COALESCE(?, description)
                            WHERE setting_key = ?
                        """, (value, description, key))
                    else:
                        # 插入新设置
                        cursor.execute("""
                            INSERT INTO system_settings (setting_key, setting_value, description)
                            VALUES (?, ?, ?)
                        """, (key, value, description or ''))

                    conn.commit()
                except Exception as e:
                    print(f"设置系统设置失败: {e}")
                    pass

    # 保持向后兼容的方法名
    def get_setting(self, key, default_value=None):
        """获取系统设置（向后兼容）"""
        return self.get_system_setting(key, default_value)

    def set_setting(self, key, value, description=None):
        """设置系统设置（向后兼容）"""
        return self.set_system_setting(key, value, description)

    # 卡片管理方法
    def create_card(self, card_code, points_value, price, created_by):
        """创建卡片"""
        from datetime import datetime
        with self.get_connection() as conn:
            cursor = conn.cursor()

            # 检查表结构，确定使用哪个字段
            cursor.execute("PRAGMA table_info(card)")
            columns = [column[1] for column in cursor.fetchall()]

            if 'price' in columns:
                # 如果有 price 字段，同时更新 currency_amount（如果存在）
                if 'currency_amount' in columns:
                    cursor.execute("""
                        INSERT INTO card (card_code, points_value, price, currency_amount, created_by, created_at)
                        VALUES (?, ?, ?, ?, ?, ?)
                    """, (card_code, points_value, price, int(price), created_by, datetime.now().strftime('%Y-%m-%d %H:%M:%S')))
                else:
                    cursor.execute("""
                        INSERT INTO card (card_code, points_value, price, created_by, created_at)
                        VALUES (?, ?, ?, ?, ?)
                    """, (card_code, points_value, price, created_by, datetime.now().strftime('%Y-%m-%d %H:%M:%S')))
            else:
                # 如果没有 price 字段，使用 currency_amount
                cursor.execute("""
                    INSERT INTO card (card_code, points_value, currency_amount, created_by, created_at)
                    VALUES (?, ?, ?, ?, ?)
                """, (card_code, points_value, int(price), created_by, datetime.now().strftime('%Y-%m-%d %H:%M:%S')))

            conn.commit()
            return cursor.lastrowid

    def get_cards_paginated(self, offset, limit, status='all'):
        """获取分页卡密列表"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            # 检查表结构，确定使用哪个字段
            cursor.execute("PRAGMA table_info(card)")
            columns = [column[1] for column in cursor.fetchall()]

            # 构建查询字段
            if 'price' in columns:
                price_field = 'price'
            elif 'currency_amount' in columns:
                price_field = 'currency_amount as price'
            else:
                price_field = '0 as price'

            if status == 'used':
                cursor.execute(f"""
                    SELECT id, card_code, points_value, {price_field}, is_used, used_by, used_at, created_by, created_at
                    FROM card WHERE is_used = 1
                    ORDER BY created_at DESC LIMIT ? OFFSET ?
                """, (limit, offset))
            elif status == 'unused':
                cursor.execute(f"""
                    SELECT id, card_code, points_value, {price_field}, is_used, used_by, used_at, created_by, created_at
                    FROM card WHERE is_used = 0
                    ORDER BY created_at DESC LIMIT ? OFFSET ?
                """, (limit, offset))
            else:
                cursor.execute(f"""
                    SELECT id, card_code, points_value, {price_field}, is_used, used_by, used_at, created_by, created_at
                    FROM card ORDER BY created_at DESC LIMIT ? OFFSET ?
                """, (limit, offset))

            cards = []
            for row in cursor.fetchall():
                card = dict(row)
                # 确保时间字段是 datetime 对象
                if card.get('created_at') is not None:
                    try:
                        card['created_at'] = self._parse_datetime(card['created_at'])
                    except Exception as e:
                        print(f"Warning: Failed to parse created_at '{card.get('created_at')}': {e}")
                        card['created_at'] = self._parse_datetime(None)

                if card.get('used_at') is not None:
                    try:
                        card['used_at'] = self._parse_datetime(card['used_at'])
                    except Exception as e:
                        print(f"Warning: Failed to parse used_at '{card.get('used_at')}': {e}")
                        card['used_at'] = self._parse_datetime(None)
                cards.append(card)

            return cards

    def get_card_count(self, status='all'):
        """获取卡密总数"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            if status == 'used':
                cursor.execute("SELECT COUNT(*) FROM card WHERE is_used = 1")
            elif status == 'unused':
                cursor.execute("SELECT COUNT(*) FROM card WHERE is_used = 0")
            else:
                cursor.execute("SELECT COUNT(*) FROM card")

            result = cursor.fetchone()
            return result[0] if result else 0

    def get_users_paginated(self, offset, limit, is_admin=None):
        """获取分页用户列表"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            # 检查表结构，确定是否有 last_login 字段
            cursor.execute("PRAGMA table_info(user)")
            columns = [column[1] for column in cursor.fetchall()]

            # 构建查询字段
            if 'last_login' in columns:
                select_fields = "id, username, email, password_hash, is_admin, is_super_admin, points, created_at, last_login"
            else:
                select_fields = "id, username, email, password_hash, is_admin, is_super_admin, points, created_at, NULL as last_login"

            if is_admin is not None:
                cursor.execute(f"""
                    SELECT {select_fields} FROM user WHERE is_admin = ?
                    ORDER BY created_at DESC LIMIT ? OFFSET ?
                """, (is_admin, limit, offset))
            else:
                cursor.execute(f"""
                    SELECT {select_fields} FROM user ORDER BY created_at DESC LIMIT ? OFFSET ?
                """, (limit, offset))

            users = []
            for row in cursor.fetchall():
                user = dict(row)
                # 确保时间字段是 datetime 对象
                if user.get('created_at'):
                    user['created_at'] = self._parse_datetime(user['created_at'])
                if user.get('last_login'):
                    user['last_login'] = self._parse_datetime(user['last_login'])
                users.append(user)

            return users

    def get_admin_count(self):
        """获取管理员总数"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM user WHERE is_admin = 1")
            result = cursor.fetchone()
            return result[0] if result else 0

    def create_admin_user(self, username, email, password_hash, is_admin=True, is_super_admin=False, points=0):
        """创建管理员用户"""
        from datetime import datetime
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                INSERT INTO user (username, email, password_hash, is_admin, is_super_admin, points, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (username, email, password_hash, is_admin, is_super_admin, points, datetime.now().strftime('%Y-%m-%d %H:%M:%S')))
            conn.commit()
            return cursor.lastrowid

    def update_user_admin_status(self, user_id, is_admin, is_super_admin):
        """更新用户管理员状态"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE user SET is_admin = ?, is_super_admin = ? WHERE id = ?
            """, (is_admin, is_super_admin, user_id))
            conn.commit()
            return cursor.rowcount > 0

    # 软件管理方法
    def get_software_by_name(self, name):
        """根据名称获取软件"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM software WHERE name = ?", (name,))
            result = cursor.fetchone()
            return dict(result) if result else None

    def create_software(self, name, description, points_per_use, api_key, created_by):
        """创建软件"""
        from datetime import datetime
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                INSERT INTO software (name, description, points_per_use, api_key, created_by, created_at, is_active)
                VALUES (?, ?, ?, ?, ?, ?, 1)
            """, (name, description, points_per_use, api_key, created_by, datetime.now().strftime('%Y-%m-%d %H:%M:%S')))
            conn.commit()
            return cursor.lastrowid

    def get_all_software(self):
        """获取所有软件"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM software ORDER BY created_at DESC")

            software_list = []
            for row in cursor.fetchall():
                software = dict(row)
                # 确保时间字段是 datetime 对象
                if software.get('created_at'):
                    software['created_at'] = self._parse_datetime(software['created_at'])
                software_list.append(software)

            return software_list

    def get_software_by_id(self, software_id):
        """根据ID获取软件"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM software WHERE id = ?", (software_id,))
            result = cursor.fetchone()
            return dict(result) if result else None

    def update_software_status(self, software_id, is_active):
        """更新软件状态"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("UPDATE software SET is_active = ? WHERE id = ?", (is_active, software_id))
            conn.commit()
            return cursor.rowcount > 0

    def update_software(self, software_id, name, description, points_per_use):
        """更新软件信息"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            # 检查表结构，确定是否有 updated_at 字段
            cursor.execute("PRAGMA table_info(software)")
            columns = [column[1] for column in cursor.fetchall()]

            if 'updated_at' in columns:
                cursor.execute("""
                    UPDATE software
                    SET name = ?, description = ?, points_per_use = ?, updated_at = ?
                    WHERE id = ?
                """, (name, description, points_per_use, current_time, software_id))
            else:
                cursor.execute("""
                    UPDATE software
                    SET name = ?, description = ?, points_per_use = ?
                    WHERE id = ?
                """, (name, description, points_per_use, software_id))

            conn.commit()
            return cursor.rowcount > 0

    # 消费记录管理方法
    def get_consumption_records_paginated(self, offset, limit):
        """获取分页消费记录"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT cr.*, u.username, s.name as software_name
                FROM consumption_record cr
                LEFT JOIN user u ON cr.user_id = u.id
                LEFT JOIN software s ON cr.software_id = s.id
                ORDER BY cr.timestamp DESC
                LIMIT ? OFFSET ?
            """, (limit, offset))
            return [dict(row) for row in cursor.fetchall()]

    def get_consumption_record_count(self):
        """获取消费记录总数"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM consumption_record")
            result = cursor.fetchone()
            return result[0] if result else 0

    def get_software_consumption_stats(self):
        """获取按软件统计的消费数据"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT s.name,
                       COALESCE(SUM(cr.points_consumed), 0) as total_consumed,
                       COUNT(cr.id) as usage_count
                FROM software s
                LEFT JOIN consumption_record cr ON s.id = cr.software_id
                GROUP BY s.id, s.name
                ORDER BY total_consumed DESC
            """)
            results = []
            for row in cursor.fetchall():
                row_dict = dict(row)
                # 确保数值字段不为 None
                row_dict['total_consumed'] = row_dict.get('total_consumed') or 0
                row_dict['usage_count'] = row_dict.get('usage_count') or 0
                results.append(row_dict)
            return results

    def get_daily_consumption_stats(self, days=30):
        """获取按日期统计的消费数据"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT DATE(timestamp) as date,
                       COALESCE(SUM(points_consumed), 0) as total_consumed
                FROM consumption_record
                WHERE timestamp >= datetime('now', '-{} days')
                GROUP BY DATE(timestamp)
                ORDER BY date DESC
            """.format(days))
            results = []
            for row in cursor.fetchall():
                row_dict = dict(row)
                # 确保数值字段不为 None
                row_dict['total_consumed'] = row_dict.get('total_consumed') or 0

                # 将日期字符串转换为日期对象
                if row_dict.get('date'):
                    try:
                        from datetime import datetime
                        row_dict['date'] = datetime.strptime(row_dict['date'], '%Y-%m-%d').date()
                    except (ValueError, TypeError):
                        row_dict['date'] = None
                else:
                    row_dict['date'] = None

                results.append(row_dict)
            return results

    def update_software_api_key(self, software_id, api_key):
        """更新软件API密钥"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("UPDATE software SET api_key = ? WHERE id = ?", (api_key, software_id))
            conn.commit()
            return cursor.rowcount > 0

    # API相关方法
    def get_software_by_api_key(self, api_key):
        """根据API密钥获取软件信息"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM software WHERE api_key = ?", (api_key,))
            row = cursor.fetchone()
            return dict(row) if row else None

    def log_api_call(self, api_key, endpoint, user_id=None, success=True, error_message=None, ip_address=None):
        """记录API调用日志"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            # 检查api_logs表是否存在，如果不存在则创建
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS api_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    api_key TEXT NOT NULL,
                    endpoint TEXT NOT NULL,
                    user_id INTEGER,
                    success BOOLEAN NOT NULL,
                    error_message TEXT,
                    ip_address TEXT,
                    timestamp TEXT NOT NULL
                )
            """)

            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            cursor.execute("""
                INSERT INTO api_logs (api_key, endpoint, user_id, success, error_message, ip_address, timestamp)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (api_key, endpoint, user_id, success, error_message, ip_address, current_time))
            conn.commit()
            return cursor.lastrowid

    def update_user_last_login(self, user_id):
        """更新用户最后登录时间"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            # 检查last_login列是否存在，如果不存在则添加
            try:
                cursor.execute("ALTER TABLE user ADD COLUMN last_login TEXT")
                conn.commit()
            except:
                pass  # 列已存在

            # 使用标准的 datetime 格式
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            cursor.execute("""
                UPDATE user SET last_login = ? WHERE id = ?
            """, (current_time, user_id))
            conn.commit()
            return cursor.rowcount > 0

    def deduct_user_points(self, user_id, points, software_id, ip_address=None, user_agent=None):
        """扣除用户点数并记录消费"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            # 开始事务
            cursor.execute("BEGIN TRANSACTION")

            try:
                # 扣除点数
                cursor.execute("UPDATE user SET points = points - ? WHERE id = ?", (points, user_id))

                # 记录消费
                current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                cursor.execute("""
                    INSERT INTO consumption_record (user_id, software_id, points_consumed, ip_address, user_agent, timestamp)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (user_id, software_id, points, ip_address, user_agent, current_time))

                record_id = cursor.lastrowid

                # 提交事务
                cursor.execute("COMMIT")
                return record_id

            except Exception as e:
                # 回滚事务
                cursor.execute("ROLLBACK")
                raise e

    def get_consumption_records(self, software_id=None, username=None, limit=50):
        """获取消费记录"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            query = """
                SELECT cr.*, u.username
                FROM consumption_record cr
                LEFT JOIN user u ON cr.user_id = u.id
                WHERE 1=1
            """
            params = []

            if software_id:
                query += " AND cr.software_id = ?"
                params.append(software_id)

            if username:
                query += " AND u.username = ?"
                params.append(username)

            query += " ORDER BY cr.timestamp DESC LIMIT ?"
            params.append(limit)

            cursor.execute(query, params)

            records = []
            for row in cursor.fetchall():
                record = dict(row)
                records.append({
                    'record_id': record['id'],
                    'username': record['username'],
                    'points_consumed': record['points_consumed'],
                    'timestamp': record['timestamp'],
                    'ip_address': record.get('ip_address', '')
                })
            return records

# 全局数据库管理器
db_manager = DatabaseManager()

# 简单的用户类
class SimpleUser:
    def __init__(self, user_data):
        self.id = user_data['id']
        self.username = user_data['username']
        self.email = user_data['email']
        self.password_hash = user_data['password_hash']  # 存储密码哈希用于验证
        self.is_admin = bool(user_data['is_admin'])
        self.is_super_admin = bool(user_data['is_super_admin'])
        self.points = user_data['points']

        # 处理时间字段
        from datetime import datetime
        # 兼容sqlite3.Row和dict对象
        has_created_at = False
        created_at_value = None
        try:
            if hasattr(user_data, 'keys'):
                has_created_at = 'created_at' in user_data.keys()
            else:
                has_created_at = 'created_at' in user_data

            if has_created_at:
                created_at_value = user_data['created_at']
        except (KeyError, TypeError):
            has_created_at = False

        if has_created_at and created_at_value:
            if isinstance(created_at_value, str):
                try:
                    # 尝试多种时间格式
                    for fmt in ['%Y-%m-%d %H:%M:%S', '%Y-%m-%d %H:%M:%S.%f', '%Y-%m-%d']:
                        try:
                            self.created_at = datetime.strptime(created_at_value, fmt)
                            break
                        except ValueError:
                            continue
                    else:
                        # 如果所有格式都失败，使用当前时间
                        self.created_at = datetime.now()
                except Exception:
                    self.created_at = datetime.now()
            else:
                self.created_at = created_at_value
        else:
            self.created_at = datetime.now()

        # last_login 字段（可选）
        has_last_login = False
        last_login_value = None
        try:
            if hasattr(user_data, 'keys'):
                has_last_login = 'last_login' in user_data.keys()
            else:
                has_last_login = 'last_login' in user_data

            if has_last_login:
                last_login_value = user_data['last_login']
        except (KeyError, TypeError):
            has_last_login = False

        if has_last_login and last_login_value:
            if isinstance(last_login_value, str):
                try:
                    # 尝试多种时间格式
                    for fmt in ['%Y-%m-%d %H:%M:%S', '%Y-%m-%d %H:%M:%S.%f', '%Y-%m-%d']:
                        try:
                            self.last_login = datetime.strptime(last_login_value, fmt)
                            break
                        except ValueError:
                            continue
                    else:
                        self.last_login = None
                except Exception:
                    self.last_login = None
            else:
                self.last_login = last_login_value
        else:
            self.last_login = None

        self.is_authenticated = True
        self.is_active = True
        self.is_anonymous = False
    
    def get_id(self):
        return str(self.id)

    def check_password(self, password):
        """检查密码是否正确"""
        # 使用数据库管理器的密码验证方法
        return db_manager.verify_password(password, self.password_hash)

def load_user(user_id):
    """Flask-Login用户加载器"""
    user_data = db_manager.get_user_by_id(int(user_id))
    if user_data:
        return SimpleUser(user_data)
    return None


