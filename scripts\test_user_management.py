#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试用户管理功能
1. 测试用户名验证（只允许英文和数字）
2. 测试超级管理员编辑用户功能
"""

import sys
import os
import sqlite3
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_username_validation():
    """测试用户名验证功能"""
    print("🔤 测试用户名验证功能...")
    
    try:
        from routes.auth import validate_username
        
        # 测试用例
        test_cases = [
            # (用户名, 是否应该通过, 描述)
            ("admin", True, "正常英文用户名"),
            ("user123", True, "英文+数字用户名"),
            ("test", True, "短英文用户名"),
            ("a1b2c3d4e5f6g7h8i9j0", True, "20位用户名"),
            ("Admin", True, "大写字母开头"),
            ("testUser", True, "驼峰命名"),
            
            # 应该失败的用例
            ("ab", False, "用户名太短"),
            ("a1b2c3d4e5f6g7h8i9j0k", False, "用户名太长"),
            ("123abc", False, "数字开头"),
            ("test-user", False, "包含连字符"),
            ("test_user", False, "包含下划线"),
            ("test user", False, "包含空格"),
            ("测试用户", False, "包含中文"),
            ("test@user", False, "包含特殊字符"),
            ("", False, "空用户名"),
        ]
        
        passed = 0
        total = len(test_cases)
        
        for username, should_pass, description in test_cases:
            is_valid, msg = validate_username(username)
            
            if is_valid == should_pass:
                print(f"   ✅ {description}: '{username}' - {msg if msg else '通过'}")
                passed += 1
            else:
                print(f"   ❌ {description}: '{username}' - 预期{'通过' if should_pass else '失败'}，实际{'通过' if is_valid else '失败'}")
        
        print(f"   📊 用户名验证测试: {passed}/{total} 通过")
        return passed == total
        
    except Exception as e:
        print(f"   ❌ 测试用户名验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_user_update_function():
    """测试用户信息更新功能"""
    print("👤 测试用户信息更新功能...")
    
    try:
        from bypass_sqlalchemy import DatabaseManager
        
        db_manager = DatabaseManager()
        
        # 创建测试用户
        test_username = "testuser123"
        test_email = "<EMAIL>"
        test_password = "testpass123"
        
        # 检查测试用户是否已存在
        existing_user = db_manager.get_user_by_username(test_username)
        if existing_user:
            user_id = existing_user['id']
            print(f"   📋 使用现有测试用户: {test_username} (ID: {user_id})")
        else:
            # 创建测试用户
            password_hash = db_manager.hash_password(test_password)
            
            with db_manager.get_connection() as conn:
                cursor = conn.cursor()
                current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                cursor.execute("""
                    INSERT INTO user (username, email, password_hash, is_admin, is_super_admin, points, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (test_username, test_email, password_hash, False, False, 100, current_time))
                conn.commit()
                user_id = cursor.lastrowid
            
            print(f"   📋 创建测试用户: {test_username} (ID: {user_id})")
        
        # 测试更新用户信息
        new_username = "updateduser123"
        new_email = "<EMAIL>"
        new_password = "newpassword123"
        new_password_hash = db_manager.hash_password(new_password)
        
        # 执行更新
        success = db_manager.update_user_info(
            user_id=user_id,
            username=new_username,
            email=new_email,
            password_hash=new_password_hash
        )
        
        if success:
            print(f"   ✅ 用户信息更新成功")
            
            # 验证更新结果
            updated_user = db_manager.get_user_by_id(user_id)
            if (updated_user and 
                updated_user['username'] == new_username and 
                updated_user['email'] == new_email):
                print(f"   ✅ 更新验证成功: {new_username}, {new_email}")
                
                # 验证密码更新
                if db_manager.verify_password(new_password, updated_user['password_hash']):
                    print(f"   ✅ 密码更新验证成功")
                else:
                    print(f"   ❌ 密码更新验证失败")
                    return False
                
                # 清理测试用户
                with db_manager.get_connection() as conn:
                    cursor = conn.cursor()
                    cursor.execute("DELETE FROM user WHERE id = ?", (user_id,))
                    conn.commit()
                print(f"   🗑️  测试用户已清理")
                
                return True
            else:
                print(f"   ❌ 更新验证失败")
                return False
        else:
            print(f"   ❌ 用户信息更新失败")
            return False
            
    except Exception as e:
        print(f"   ❌ 测试用户信息更新失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_super_admin_permissions():
    """测试超级管理员权限"""
    print("👑 测试超级管理员权限...")
    
    try:
        conn = sqlite3.connect('dianka.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # 检查超级管理员用户
        cursor.execute("SELECT username, is_super_admin FROM user WHERE is_super_admin = 1")
        super_admins = cursor.fetchall()
        
        print("   📋 超级管理员用户:")
        for admin in super_admins:
            print(f"      {admin['username']} (超级管理员: {admin['is_super_admin']})")
        
        if len(super_admins) > 0:
            print("   ✅ 超级管理员权限检查通过")
            print("   ℹ️  超级管理员现在可以:")
            print("      - 编辑普通用户的用户名和邮箱")
            print("      - 修改普通用户的密码")
            print("      - 管理软件扣费点数")
            print("      - 访问系统设置页面")
            print("      - 创建和管理普通管理员")
            conn.close()
            return True
        else:
            print("   ❌ 未找到超级管理员用户")
            conn.close()
            return False
            
    except Exception as e:
        print(f"   ❌ 检查超级管理员权限失败: {e}")
        return False

def test_email_validation():
    """测试邮箱验证功能"""
    print("📧 测试邮箱验证功能...")
    
    try:
        from routes.auth import validate_email
        
        # 测试用例
        test_cases = [
            # (邮箱, 是否应该通过, 描述)
            ("<EMAIL>", True, "标准邮箱格式"),
            ("<EMAIL>", True, "包含点的邮箱"),
            ("<EMAIL>", True, "包含加号的邮箱"),
            ("<EMAIL>", True, "数字开头的邮箱"),
            
            # 应该失败的用例
            ("invalid-email", False, "无@符号"),
            ("@example.com", False, "缺少用户名"),
            ("test@", False, "缺少域名"),
            ("test@.com", False, "域名格式错误"),
            ("test@example", False, "缺少顶级域名"),
            ("", False, "空邮箱"),
        ]
        
        passed = 0
        total = len(test_cases)
        
        for email, should_pass, description in test_cases:
            is_valid = validate_email(email)
            
            if is_valid == should_pass:
                print(f"   ✅ {description}: '{email}'")
                passed += 1
            else:
                print(f"   ❌ {description}: '{email}' - 预期{'通过' if should_pass else '失败'}，实际{'通过' if is_valid else '失败'}")
        
        print(f"   📊 邮箱验证测试: {passed}/{total} 通过")
        return passed == total
        
    except Exception as e:
        print(f"   ❌ 测试邮箱验证失败: {e}")
        return False

def show_usage_instructions():
    """显示使用说明"""
    print("\n📖 功能使用说明")
    print("=" * 60)
    print("🔤 用户名验证规则:")
    print("   1. 只能包含英文字母和数字")
    print("   2. 必须以英文字母开头")
    print("   3. 长度为3-20位")
    print("   4. 不能包含中文、特殊字符或空格")
    print("\n👤 超级管理员编辑用户:")
    print("   1. 使用超级管理员账号登录 (superadmin / super123)")
    print("   2. 访问 /admin/users 页面")
    print("   3. 点击用户列表中的 '编辑' 按钮")
    print("   4. 修改用户名、邮箱或密码")
    print("   5. 点击 '保存修改' 按钮")
    print("\n⚠️  注意事项:")
    print("   - 超级管理员不能编辑自己的账户")
    print("   - 修改用户名后，用户需要使用新用户名登录")
    print("   - 修改密码后，用户的当前登录状态将失效")

def main():
    """主函数"""
    print("🚀 开始测试用户管理功能")
    print("=" * 60)
    
    results = []
    
    # 1. 测试用户名验证
    results.append(("用户名验证", test_username_validation()))
    
    # 2. 测试邮箱验证
    results.append(("邮箱验证", test_email_validation()))
    
    # 3. 测试用户信息更新
    results.append(("用户信息更新", test_user_update_function()))
    
    # 4. 测试超级管理员权限
    results.append(("超级管理员权限", test_super_admin_permissions()))
    
    # 输出结果
    print("\n📊 测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 成功" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有功能测试通过！")
        show_usage_instructions()
        return True
    else:
        print("⚠️  部分测试失败，请检查系统配置。")
        return False

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n✅ 用户管理功能测试完成！")
            sys.exit(0)
        else:
            print("\n❌ 用户管理功能测试失败！")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⚠️  测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
