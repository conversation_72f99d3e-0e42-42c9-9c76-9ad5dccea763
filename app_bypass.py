#!/usr/bin/env python3
"""
绕过Flask-SQLAlchemy问题的应用启动文件
"""

from flask import Flask, render_template, redirect, url_for, jsonify
from flask_login import LoginManager
from flask_cors import CORS
from datetime import datetime

# 创建Flask应用
app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'

# 初始化扩展（不使用Flask-SQLAlchemy）
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'auth.login'

CORS(app)

# 用户加载器 - 使用绕过方案
@login_manager.user_loader
def load_user(user_id):
    from bypass_sqlalchemy import load_user as bypass_load_user
    return bypass_load_user(user_id)

# 导入路由
try:
    from routes.auth import auth_bp
    from routes.admin import admin_bp
    from routes.api import api_bp
    from routes.user import user_bp
    
    # 注册蓝图
    app.register_blueprint(auth_bp, url_prefix='/auth')
    app.register_blueprint(admin_bp, url_prefix='/admin')
    app.register_blueprint(api_bp, url_prefix='/api')
    app.register_blueprint(user_bp, url_prefix='/user')
    print("✅ 所有路由加载成功")
except Exception as e:
    print(f"⚠️  路由加载失败: {e}")
    print("应用将以基础模式启动")

@app.route('/')
def index():
    try:
        return redirect(url_for('auth.login'))
    except:
        return jsonify({
            'message': '点卡平台',
            'status': 'running',
            'database': 'point_card_platform.db',
            'mode': 'bypass_sqlalchemy'
        })

@app.route('/health')
def health():
    return jsonify({
        'status': 'ok', 
        'timestamp': datetime.now().isoformat(),
        'database': 'connected',
        'mode': 'bypass_sqlalchemy'
    })

if __name__ == '__main__':
    print("🚀 启动点卡平台（绕过模式）...")
    print("=" * 40)
    print("访问地址: http://localhost:5000")
    print("数据库文件: point_card_platform.db")
    print("运行模式: 绕过Flask-SQLAlchemy")
    print("默认账号:")
    print("  超级管理员: admin / admin123")
    print("=" * 40)

    # 检查是否有SSL证书文件
    import os
    ssl_cert = 'cert.pem'
    ssl_key = 'key.pem'

    if os.path.exists(ssl_cert) and os.path.exists(ssl_key):
        print("🔒 发现SSL证书，启用HTTPS模式")
        print("HTTPS访问地址: https://localhost:5000")
        app.run(debug=True, host='0.0.0.0', port=5000, ssl_context=(ssl_cert, ssl_key))
    else:
        print("⚠️  未发现SSL证书，使用HTTP模式")
        print("建议使用Nginx反向代理配置HTTPS")
        app.run(debug=True, host='0.0.0.0', port=5000)
