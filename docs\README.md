# 📚 文档目录

欢迎来到点卡平台文档中心！这里包含了项目的完整文档资料。

## 📋 文档列表

### 🚀 快速开始
- **[项目主页](../README.md)** - 项目概述、特性介绍和快速开始指南
- **[部署指南](DEPLOYMENT.md)** - 完整的部署说明和配置指南

### 🔧 API文档
- **[API接口文档](API_REFERENCE.md)** - 完整的API接口参考文档
- **[登录API指南](API_LOGIN_GUIDE.md)** - 登录认证API详细说明

### 📊 项目信息
- **[项目状态](PROJECT_STATUS.md)** - 当前项目状态和开发进度

## 🎯 文档导航

### 对于开发者
如果你是开发者，建议按以下顺序阅读：
1. [项目主页](../README.md) - 了解项目概况
2. [API接口文档](API_REFERENCE.md) - 学习API使用
3. [项目状态](PROJECT_STATUS.md) - 了解开发进度

### 对于运维人员
如果你负责部署和运维，建议阅读：
1. [部署指南](DEPLOYMENT.md) - 完整部署流程
2. [项目主页](../README.md) - 了解系统架构
3. [项目状态](PROJECT_STATUS.md) - 了解系统状态

### 对于API用户
如果你要集成API，建议阅读：
1. [API接口文档](API_REFERENCE.md) - 完整API参考
2. [登录API指南](API_LOGIN_GUIDE.md) - 认证API详解
3. [项目主页](../README.md) - 了解系统功能

## 📖 文档说明

### 文档格式
- 所有文档使用Markdown格式编写
- 支持代码高亮和表格
- 包含丰富的示例代码

### 更新频率
- 文档与代码同步更新
- 重大变更会及时反映在文档中
- 版本信息在各文档中标注

### 反馈建议
如果发现文档问题或有改进建议：
1. 提交Issue到项目仓库
2. 发送邮件到技术支持
3. 直接联系开发团队

## 🔗 相关链接

- **项目仓库**: [GitHub/GitLab链接]
- **在线演示**: [演示地址]
- **技术支持**: <EMAIL>
- **官方网站**: https://example.com

## 📝 文档版本

| 文档 | 版本 | 更新时间 |
|------|------|----------|
| API_REFERENCE.md | v2.0 | 2025-07-03 |
| API_LOGIN_GUIDE.md | v1.5 | 2025-07-03 |
| DEPLOYMENT.md | v2.0 | 2025-07-03 |
| PROJECT_STATUS.md | v2.0 | 2025-07-03 |

---

**最后更新**: 2025-07-03  
**维护者**: 开发团队
