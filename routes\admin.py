from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from datetime import datetime
import secrets
import string
import uuid

# 导入绕过方案
from bypass_sqlalchemy import db_manager

admin_bp = Blueprint('admin', __name__)

def generate_card_code():
    """生成卡密代码"""
    # 生成16位大写字母和数字组合的卡密
    characters = string.ascii_uppercase + string.digits
    return ''.join(secrets.choice(characters) for _ in range(16))

def admin_required(f):
    """管理员权限装饰器"""
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or not current_user.is_admin:
            flash('需要管理员权限', 'error')
            return redirect(url_for('auth.login'))
        return f(*args, **kwargs)
    decorated_function.__name__ = f.__name__
    return decorated_function

def super_admin_required(f):
    """超级管理员权限装饰器"""
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or not current_user.is_super_admin:
            flash('需要超级管理员权限', 'error')
            return redirect(url_for('admin.dashboard'))
        return f(*args, **kwargs)
    decorated_function.__name__ = f.__name__
    return decorated_function



@admin_bp.route('/dashboard')
@login_required
@admin_required
def dashboard():
    """管理员仪表板"""
    try:
        # 统计数据 - 使用绕过方案
        total_users = db_manager.get_user_count() or 0
        total_cards = db_manager.get_card_count('all') or 0
        used_cards = db_manager.get_used_card_count() or 0
        total_software = db_manager.get_software_count() or 0

        # 今日消费统计
        today = datetime.now()  # 使用 datetime 而不是 date
        today_consumption = db_manager.get_today_consumption(today) or 0

        # 最近的消费记录
        recent_records = db_manager.get_recent_consumption_records(10)

        stats = {
            'total_users': total_users,
            'total_cards': total_cards,
            'used_cards': used_cards,
            'unused_cards': (total_cards or 0) - (used_cards or 0),
            'total_software': total_software,
            'today_consumption': today_consumption
        }

        return render_template('admin/dashboard.html', stats=stats, recent_records=recent_records)

    except Exception as e:
        flash(f'获取仪表板数据失败: {str(e)}', 'error')
        return render_template('admin/dashboard.html', stats={}, recent_records=[])

@admin_bp.route('/create_cards', methods=['GET', 'POST'])
@login_required
@admin_required
def create_cards():
    """创建卡密"""
    try:
        # 检查卡密价值设置模式
        card_value_mode = db_manager.get_setting('card_value_mode', 'flexible')  # flexible 或 fixed
        fixed_card_amounts = db_manager.get_setting('fixed_card_amounts', '10,50,100,500,1000')

        if request.method == 'POST':
            try:
                amount = int(request.form.get('amount', 0))  # 改为金额输入
                quantity = int(request.form.get('quantity', 1))

                # 如果是固定模式，检查金额是否在允许范围内
                if card_value_mode == 'fixed' and not current_user.is_super_admin:
                    allowed_amounts = [int(x.strip()) for x in fixed_card_amounts.split(',')]
                    if amount not in allowed_amounts:
                        flash(f'只能创建以下面额的卡密: {fixed_card_amounts} 元', 'error')
                        return render_template('admin/create_cards.html',
                                             card_value_mode=card_value_mode,
                                             fixed_card_amounts=allowed_amounts)

                # 根据金额计算点数
                yuan_to_point_rate = float(db_manager.get_setting('yuan_to_point_rate', '100'))  # 默认1元=100点
                points_value = amount * yuan_to_point_rate

                if amount <= 0 or quantity <= 0:
                    flash('请输入有效的数值', 'error')
                    return render_template('admin/create_cards.html',
                                         card_value_mode=card_value_mode,
                                         fixed_card_amounts=fixed_card_amounts.split(',') if card_value_mode == 'fixed' else None,
                                         yuan_to_point_rate=yuan_to_point_rate)

                if quantity > 100:
                    flash('单次最多创建100张卡密', 'error')
                    return render_template('admin/create_cards.html',
                                         card_value_mode=card_value_mode,
                                         fixed_card_amounts=fixed_card_amounts.split(',') if card_value_mode == 'fixed' else None,
                                         yuan_to_point_rate=yuan_to_point_rate)

                created_cards = []
                for _ in range(quantity):
                    card_code = generate_card_code()
                    card_id = db_manager.create_card(
                        card_code=card_code,
                        points_value=int(points_value),
                        price=float(amount),
                        created_by=current_user.id
                    )

                    # 创建卡片对象用于模板显示
                    card_obj = {
                        'id': card_id,
                        'card_code': card_code,
                        'points_value': int(points_value),
                        'price': float(amount),
                        'created_by': current_user.id
                    }
                    created_cards.append(card_obj)

                flash(f'成功创建 {quantity} 张卡密，每张 {amount} 元 = {int(points_value)} 点', 'success')
                return render_template('admin/created_cards.html', cards=created_cards)

            except ValueError:
                flash('请输入有效的数字', 'error')
            except Exception as e:
                flash(f'创建卡密失败: {str(e)}', 'error')

        # 获取汇率和固定面额列表
        yuan_to_point_rate = float(db_manager.get_setting('yuan_to_point_rate', '100'))
        fixed_amounts = fixed_card_amounts.split(',') if card_value_mode == 'fixed' else None
        return render_template('admin/create_cards.html',
                             card_value_mode=card_value_mode,
                             fixed_card_amounts=fixed_amounts,
                             yuan_to_point_rate=yuan_to_point_rate)

    except Exception as e:
        flash(f'获取设置失败: {str(e)}', 'error')
        return render_template('admin/create_cards.html',
                             card_value_mode='flexible',
                             fixed_card_amounts=None,
                         yuan_to_point_rate=yuan_to_point_rate)

@admin_bp.route('/cards')
@login_required
@admin_required
def cards():
    """卡密管理"""
    page = request.args.get('page', 1, type=int)
    status = request.args.get('status', 'all')
    per_page = 20
    offset = (page - 1) * per_page

    try:
        # 获取卡密列表
        cards_data = db_manager.get_cards_paginated(offset, per_page, status)
        total_cards = db_manager.get_card_count(status)

        # 为每个卡密加载用户信息
        for card in cards_data:
            if card.get('used_by'):
                user_info = db_manager.get_user_by_id(card['used_by'])
                card['user'] = user_info

        # 创建分页对象
        class PaginationMock:
            def __init__(self, items, page, per_page, total):
                self.items = items
                self.page = page
                self.per_page = per_page
                self.total = total or 0
                self.pages = (self.total + per_page - 1) // per_page if self.total > 0 else 1
                self.has_prev = page > 1
                self.has_next = page < self.pages
                self.prev_num = page - 1 if self.has_prev else None
                self.next_num = page + 1 if self.has_next else None

        cards = PaginationMock(cards_data, page, per_page, total_cards)
        return render_template('admin/cards.html', cards=cards, status=status)

    except Exception as e:
        flash(f'获取卡密列表失败: {str(e)}', 'error')
        # 重新定义 PaginationMock 类以防异常处理中使用
        class PaginationMock:
            def __init__(self, items, page, per_page, total):
                self.items = items
                self.page = page
                self.per_page = per_page
                self.total = total or 0
                self.pages = (self.total + per_page - 1) // per_page if self.total > 0 else 1
                self.has_prev = page > 1
                self.has_next = page < self.pages
                self.prev_num = page - 1 if self.has_prev else None
                self.next_num = page + 1 if self.has_next else None
        return render_template('admin/cards.html', cards=PaginationMock([], page, per_page, 0), status=status)

@admin_bp.route('/software', methods=['GET', 'POST'])
@login_required
@admin_required
def software():
    """软件管理"""
    if request.method == 'POST':
        name = request.form.get('name', '').strip()
        description = request.form.get('description', '').strip()
        points_per_use = request.form.get('points_per_use', 0, type=int)

        if not name or points_per_use <= 0:
            flash('请填写软件名称和有效的扣费点数', 'error')
            return render_template('admin/software.html')

        try:
            # 检查软件名是否已存在
            if db_manager.get_software_by_name(name):
                flash('软件名称已存在', 'error')
                return render_template('admin/software.html')

            # 生成API密钥
            import secrets
            api_key = secrets.token_urlsafe(32)

            # 创建软件
            software_id = db_manager.create_software(
                name=name,
                description=description,
                points_per_use=points_per_use,
                api_key=api_key,
                created_by=current_user.id
            )

            if software_id:
                flash(f'软件 "{name}" 创建成功，API密钥: {api_key}', 'success')
            else:
                flash('创建软件失败，请重试', 'error')

        except Exception as e:
            flash(f'创建软件失败: {str(e)}', 'error')

    try:
        # 获取所有软件
        software_list = db_manager.get_all_software()
        return render_template('admin/software.html', software_list=software_list)
    except Exception as e:
        flash(f'获取软件列表失败: {str(e)}', 'error')
        return render_template('admin/software.html', software_list=[])

@admin_bp.route('/software/<int:software_id>/edit', methods=['GET', 'POST'])
@login_required
@super_admin_required
def edit_software(software_id):
    """编辑软件（仅超级管理员）"""
    try:
        software = db_manager.get_software_by_id(software_id)
        if not software:
            flash('软件不存在', 'error')
            return redirect(url_for('admin.software'))

        if request.method == 'POST':
            name = request.form.get('name', '').strip()
            description = request.form.get('description', '').strip()
            points_per_use = request.form.get('points_per_use', 0, type=int)

            if not name or points_per_use <= 0:
                flash('请填写软件名称和有效的扣费点数', 'error')
                return render_template('admin/edit_software.html', software=software)

            # 检查软件名是否已被其他软件使用
            existing_software = db_manager.get_software_by_name(name)
            if existing_software and existing_software['id'] != software_id:
                flash('软件名称已被其他软件使用', 'error')
                return render_template('admin/edit_software.html', software=software)

            # 更新软件信息
            success = db_manager.update_software(
                software_id=software_id,
                name=name,
                description=description,
                points_per_use=points_per_use
            )

            if success:
                flash(f'软件 "{name}" 更新成功', 'success')
                return redirect(url_for('admin.software'))
            else:
                flash('更新软件失败，请重试', 'error')

        return render_template('admin/edit_software.html', software=software)

    except Exception as e:
        flash(f'编辑软件失败: {str(e)}', 'error')
        return redirect(url_for('admin.software'))

@admin_bp.route('/software/<int:software_id>/toggle')
@login_required
@admin_required
def toggle_software(software_id):
    """启用/禁用软件"""
    try:
        software = db_manager.get_software_by_id(software_id)
        if not software:
            flash('软件不存在', 'error')
            return redirect(url_for('admin.software'))

        # 切换状态
        new_status = not software.get('is_active', True)
        db_manager.update_software_status(software_id, new_status)

        status = "启用" if new_status else "禁用"
        flash(f'软件 "{software["name"]}" 已{status}', 'success')
        return redirect(url_for('admin.software'))

    except Exception as e:
        flash(f'切换软件状态失败: {str(e)}', 'error')
        return redirect(url_for('admin.software'))

@admin_bp.route('/software/<int:software_id>/regenerate_key')
@login_required
@admin_required
def regenerate_api_key(software_id):
    """重新生成API密钥"""
    try:
        software = db_manager.get_software_by_id(software_id)
        if not software:
            flash('软件不存在', 'error')
            return redirect(url_for('admin.software'))

        # 生成新的API密钥
        import secrets
        new_key = secrets.token_urlsafe(32)

        # 更新API密钥
        db_manager.update_software_api_key(software_id, new_key)

        flash(f'API密钥已重新生成: {new_key}', 'success')
        return redirect(url_for('admin.software'))

    except Exception as e:
        flash(f'重新生成API密钥失败: {str(e)}', 'error')
        return redirect(url_for('admin.software'))

@admin_bp.route('/users')
@login_required
@admin_required
def users():
    """用户管理"""
    page = request.args.get('page', 1, type=int)
    per_page = 20
    offset = (page - 1) * per_page

    try:
        # 获取普通用户列表
        users_data = db_manager.get_users_paginated(offset, per_page, is_admin=False)
        total_users = db_manager.get_user_count()

        # 创建分页对象
        class PaginationMock:
            def __init__(self, items, page, per_page, total):
                self.items = items
                self.page = page
                self.per_page = per_page
                self.total = total or 0
                self.pages = (self.total + per_page - 1) // per_page if self.total > 0 else 1
                self.has_prev = page > 1
                self.has_next = page < self.pages
                self.prev_num = page - 1 if self.has_prev else None
                self.next_num = page + 1 if self.has_next else None

        users = PaginationMock(users_data, page, per_page, total_users)
        return render_template('admin/users.html', users=users)

    except Exception as e:
        flash(f'获取用户列表失败: {str(e)}', 'error')
        return render_template('admin/users.html', users=PaginationMock([], page, per_page, 0))

@admin_bp.route('/consumption_records')
@login_required
@admin_required
def consumption_records():
    """消费记录管理"""
    page = request.args.get('page', 1, type=int)
    per_page = 50
    offset = (page - 1) * per_page

    try:
        # 获取消费记录
        records_data = db_manager.get_consumption_records_paginated(offset, per_page)
        total_records = db_manager.get_consumption_record_count()

        # 创建分页对象
        class PaginationMock:
            def __init__(self, items, page, per_page, total):
                self.items = items
                self.page = page
                self.per_page = per_page
                self.total = total or 0
                self.pages = (self.total + per_page - 1) // per_page if self.total > 0 else 1
                self.has_prev = page > 1
                self.has_next = page < self.pages
                self.prev_num = page - 1 if self.has_prev else None
                self.next_num = page + 1 if self.has_next else None

        records = PaginationMock(records_data, page, per_page, total_records)
        return render_template('admin/consumption_records.html', records=records)

    except Exception as e:
        flash(f'获取消费记录失败: {str(e)}', 'error')
        return render_template('admin/consumption_records.html', records=PaginationMock([], page, per_page, 0))

@admin_bp.route('/statistics')
@login_required
@admin_required
def statistics():
    """统计报表"""
    try:
        # 按软件统计消费
        software_stats = db_manager.get_software_consumption_stats()

        # 按日期统计消费
        daily_stats = db_manager.get_daily_consumption_stats(30)

        return render_template('admin/statistics.html',
                             software_stats=software_stats,
                             daily_stats=daily_stats)

    except Exception as e:
        flash(f'获取统计数据失败: {str(e)}', 'error')
        return render_template('admin/statistics.html',
                             software_stats=[],
                             daily_stats=[])

@admin_bp.route('/system_settings', methods=['GET', 'POST'])
@login_required
@super_admin_required
def system_settings():
    """系统设置（仅超级管理员）"""
    if request.method == 'POST':
        try:
            # 卡密价值模式
            card_value_mode = request.form.get('card_value_mode', 'flexible')
            db_manager.set_system_setting('card_value_mode', card_value_mode, '卡密价值模式：flexible=自由设置，fixed=固定面额')

            # 固定卡密面额
            if card_value_mode == 'fixed':
                fixed_amounts = request.form.get('fixed_card_amounts', '10,50,100,500,1000')
                db_manager.set_system_setting('fixed_card_amounts', fixed_amounts, '固定卡密金额列表')

            # 汇率设置
            yuan_to_point_rate = request.form.get('yuan_to_point_rate', '100')
            db_manager.set_system_setting('yuan_to_point_rate', yuan_to_point_rate, '人民币到点数汇率')

            # API安全设置
            api_rate_limit = request.form.get('api_rate_limit', '100')
            db_manager.set_system_setting('api_rate_limit', api_rate_limit, 'API每分钟调用限制')

            # 验证模式
            verification_mode = request.form.get('verification_mode', 'simple')
            db_manager.set_system_setting('verification_mode', verification_mode, 'API验证模式：simple=简单验证，strict=严格验证')

            flash('系统设置已更新', 'success')
        except Exception as e:
            flash('设置更新失败，请重试', 'error')

    # 获取当前设置
    settings = {
        'card_value_mode': db_manager.get_system_setting('card_value_mode', 'flexible'),
        'fixed_card_amounts': db_manager.get_system_setting('fixed_card_amounts', '10,50,100,500,1000'),
        'yuan_to_point_rate': db_manager.get_system_setting('yuan_to_point_rate', '100'),
        'api_rate_limit': db_manager.get_system_setting('api_rate_limit', '100'),
        'verification_mode': db_manager.get_system_setting('verification_mode', 'simple')
    }

    return render_template('admin/system_settings.html', settings=settings)

@admin_bp.route('/create_admin', methods=['GET', 'POST'])
@login_required
@super_admin_required
def create_admin():
    """创建管理员（仅超级管理员）"""
    if request.method == 'POST':
        try:
            username = request.form.get('username', '').strip()
            email = request.form.get('email', '').strip()
            password = request.form.get('password', '')
            confirm_password = request.form.get('confirm_password', '')
            is_super_admin = bool(request.form.get('is_super_admin'))

            # 验证输入
            if not username or not email or not password:
                flash('所有字段都是必填的', 'error')
                return render_template('admin/create_admin.html')

            if len(password) < 6:
                flash('密码长度至少6位', 'error')
                return render_template('admin/create_admin.html')

            if password != confirm_password:
                flash('两次输入的密码不一致', 'error')
                return render_template('admin/create_admin.html')

            # 检查用户名和邮箱是否已存在
            if db_manager.get_user_by_username(username):
                flash('用户名已存在', 'error')
                return render_template('admin/create_admin.html')

            if db_manager.get_user_by_email(email):
                flash('邮箱已被使用', 'error')
                return render_template('admin/create_admin.html')

            # 创建管理员账户
            import hashlib
            password_hash = hashlib.pbkdf2_hmac('sha256', password.encode('utf-8'), b'salt', 100000).hex()

            user_id = db_manager.create_admin_user(
                username=username,
                email=email,
                password_hash=password_hash,
                is_admin=True,
                is_super_admin=is_super_admin,
                points=0
            )

            if user_id:
                admin_type = "超级管理员" if is_super_admin else "管理员"
                flash(f'{admin_type}账户 "{username}" 创建成功', 'success')
                return redirect(url_for('admin.manage_admins'))
            else:
                flash('创建管理员失败，请重试', 'error')

        except Exception as e:
            flash(f'创建管理员失败: {str(e)}', 'error')

    return render_template('admin/create_admin.html')

@admin_bp.route('/manage_admins')
@login_required
@super_admin_required
def manage_admins():
    """管理员管理（仅超级管理员）"""
    page = request.args.get('page', 1, type=int)
    per_page = 20
    offset = (page - 1) * per_page

    try:
        # 获取管理员列表
        admins_data = db_manager.get_users_paginated(offset, per_page, is_admin=True)
        total_admins = db_manager.get_admin_count()

        # 创建分页对象
        class PaginationMock:
            def __init__(self, items, page, per_page, total):
                self.items = items
                self.page = page
                self.per_page = per_page
                self.total = total or 0
                self.pages = (self.total + per_page - 1) // per_page if self.total > 0 else 1
                self.has_prev = page > 1
                self.has_next = page < self.pages
                self.prev_num = page - 1 if self.has_prev else None
                self.next_num = page + 1 if self.has_next else None

        admins = PaginationMock(admins_data, page, per_page, total_admins)
        return render_template('admin/manage_admins.html', admins=admins)

    except Exception as e:
        flash(f'获取管理员列表失败: {str(e)}', 'error')
        return render_template('admin/manage_admins.html', admins=PaginationMock([], page, per_page, 0))

@admin_bp.route('/toggle_admin/<int:user_id>')
@login_required
@super_admin_required
def toggle_admin_status(user_id):
    """切换管理员状态"""
    try:
        user = db_manager.get_user_by_id(user_id)
        if not user:
            flash('用户不存在', 'error')
            return redirect(url_for('admin.manage_admins'))

        # 不能修改自己的状态
        if user['id'] == current_user.id:
            flash('不能修改自己的管理员状态', 'error')
            return redirect(url_for('admin.manage_admins'))

        # 切换管理员状态
        new_admin_status = not user.get('is_admin', False)
        new_super_admin_status = user.get('is_super_admin', False)

        # 取消管理员时也取消超级管理员
        if not new_admin_status:
            new_super_admin_status = False

        db_manager.update_user_admin_status(user_id, new_admin_status, new_super_admin_status)

        status = "启用" if new_admin_status else "禁用"
        flash(f'用户 "{user["username"]}" 的管理员权限已{status}', 'success')
        return redirect(url_for('admin.manage_admins'))

    except Exception as e:
        flash(f'修改管理员状态失败: {str(e)}', 'error')
        return redirect(url_for('admin.manage_admins'))

@admin_bp.route('/toggle_super_admin/<int:user_id>')
@login_required
@super_admin_required
def toggle_super_admin_status(user_id):
    """切换超级管理员状态"""
    try:
        user = db_manager.get_user_by_id(user_id)
        if not user:
            flash('用户不存在', 'error')
            return redirect(url_for('admin.manage_admins'))

        # 不能修改自己的状态
        if user['id'] == current_user.id:
            flash('不能修改自己的超级管理员状态', 'error')
            return redirect(url_for('admin.manage_admins'))

        # 只有管理员才能成为超级管理员
        if not user.get('is_admin', False):
            flash('只有管理员才能设置为超级管理员', 'error')
            return redirect(url_for('admin.manage_admins'))

        # 切换超级管理员状态
        new_super_admin_status = not user.get('is_super_admin', False)
        db_manager.update_user_admin_status(user_id, user.get('is_admin', False), new_super_admin_status)

        status = "启用" if new_super_admin_status else "禁用"
        flash(f'用户 "{user["username"]}" 的超级管理员权限已{status}', 'success')
        return redirect(url_for('admin.manage_admins'))

    except Exception as e:
        flash(f'修改超级管理员状态失败: {str(e)}', 'error')
        return redirect(url_for('admin.manage_admins'))
