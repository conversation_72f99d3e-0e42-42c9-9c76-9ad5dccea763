<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}点卡平台{% endblock %}</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    <nav class="navbar">
        <div class="container">
            <h1><a href="{{ url_for('index') }}" style="color: white; text-decoration: none;">点卡平台</a></h1>
            <div>
                {% if current_user.is_authenticated %}
                    {% if current_user.is_admin %}
                        <a href="{{ url_for('admin.dashboard') }}" class="admin-link">管理后台</a>
                        <a href="{{ url_for('admin.users') }}" class="admin-link">用户管理</a>
                        <a href="{{ url_for('admin.software') }}" class="admin-link">软件管理</a>
                        <a href="{{ url_for('admin.cards') }}" class="admin-link">卡密管理</a>
                        {% if current_user.is_super_admin %}
                        <a href="{{ url_for('admin.manage_admins') }}" class="danger-link">管理员管理</a>
                        <a href="{{ url_for('admin.system_settings') }}" class="danger-link">系统设置</a>
                        {% endif %}
                    {% else %}
                        <a href="{{ url_for('user.dashboard') }}" class="user-link">我的账户</a>
                        <a href="{{ url_for('user.points') }}" class="user-link">点数详情</a>
                        <a href="{{ url_for('user.recharge') }}" class="user-link">充值</a>
                        <a href="{{ url_for('user.history') }}" class="user-link">使用历史</a>
                    {% endif %}
                    <a href="{{ url_for('auth.change_password') }}" class="user-link">修改密码</a>
                    <a href="{{ url_for('auth.logout') }}" class="danger-link">退出登录</a>
                    <span style="margin-left: 20px; font-weight: 600; color: #f8f9fa;">欢迎, {{ current_user.username }}!</span>
                {% else %}
                    <a href="{{ url_for('auth.login') }}">登录</a>
                    <a href="{{ url_for('auth.register') }}">注册</a>
                {% endif %}
            </div>
        </div>
    </nav>

    <main class="main-content">
        <div class="container">
            <!-- Flash消息 -->
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    <div class="flash-messages">
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'error' if category == 'error' else category }}">
                                {{ message }}
                            </div>
                        {% endfor %}
                    </div>
                {% endif %}
            {% endwith %}

            {% block content %}{% endblock %}
        </div>
    </main>

    <footer style="background-color: #2c3e50; color: white; text-align: center; padding: 1rem 0; margin-top: 2rem;">
        <div class="container">
            <p>&copy; 2024 点卡平台. 保留所有权利.</p>
        </div>
    </footer>

    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    {% block scripts %}{% endblock %}
</body>
</html>
