{% extends "base.html" %}

{% block title %}使用历史 - 点卡平台{% endblock %}

{% block content %}
<div class="card">
    <h2>📈 使用历史</h2>
    <p style="color: #666; margin-bottom: 2rem;">查看您的详细使用记录和消费历史</p>
    
    {% if records %}
    <table class="table">
        <thead>
            <tr>
                <th>记录ID</th>
                <th>软件名称</th>
                <th>消费点数</th>
                <th>使用时间</th>
                <th>IP地址</th>
            </tr>
        </thead>
        <tbody>
            {% for record in records %}
            <tr>
                <td>#{{ loop.index + (page - 1) * 20 }}</td>
                <td>
                    <span class="software-badge">
                        {{ record.software_name or '未知软件' }}
                    </span>
                </td>
                <td>
                    <span class="points-consumed">-{{ record.points_consumed }}</span>
                </td>
                <td>{{ record.timestamp }}</td>
                <td>
                    <span class="ip-address">{{ record.ip_address or '未知' }}</span>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    
    <!-- 分页导航 -->
    {% if total_pages > 1 %}
    <div class="pagination">
        {% if page > 1 %}
            <a href="{{ url_for('user.history', page=page-1) }}" class="btn btn-sm btn-secondary">&laquo; 上一页</a>
        {% endif %}
        
        <span class="page-info">
            第 {{ page }} 页，共 {{ total_pages }} 页 (总计 {{ total }} 条记录)
        </span>
        
        {% if page < total_pages %}
            <a href="{{ url_for('user.history', page=page+1) }}" class="btn btn-sm btn-secondary">下一页 &raquo;</a>
        {% endif %}
    </div>
    {% endif %}
    
    {% else %}
    <div class="empty-state">
        <div class="empty-icon">📊</div>
        <h3>暂无使用记录</h3>
        <p>您还没有任何软件使用记录</p>
        <a href="{{ url_for('user.software_list') }}" class="btn btn-primary">查看可用软件</a>
    </div>
    {% endif %}
</div>

<div style="text-align: center; margin-top: 2rem;">
    <a href="{{ url_for('user.dashboard') }}" class="btn btn-secondary">返回用户中心</a>
    <a href="{{ url_for('user.points') }}" class="btn btn-info">查看点数详情</a>
</div>

<style>
.software-badge {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.875rem;
    font-weight: 500;
}

.points-consumed {
    color: #e74c3c;
    font-weight: bold;
    font-size: 1.1rem;
}

.ip-address {
    font-family: 'Courier New', monospace;
    background: #f8f9fa;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.875rem;
}

.pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid #eee;
}

.page-info {
    color: #666;
    font-size: 0.9rem;
}

.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: #666;
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.empty-state h3 {
    margin: 1rem 0 0.5rem 0;
    color: #333;
}

.empty-state p {
    margin-bottom: 2rem;
}

.table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #333;
}

.table tbody tr:hover {
    background: #f8f9fa;
}
</style>
{% endblock %}
