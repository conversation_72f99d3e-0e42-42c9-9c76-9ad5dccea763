#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的API测试套件
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import requests
import json
import time

class APITester:
    def __init__(self, base_url="http://localhost:5000"):
        self.base_url = base_url
        self.token = None
        
    def test_api_status(self):
        """测试API状态"""
        print("1️⃣ 测试API状态...")
        try:
            response = requests.get(f"{self.base_url}/api/status", timeout=5)
            if response.status_code == 200:
                result = response.json()
                print(f"   ✅ API服务正常: {result['message']}")
                return True
            else:
                print(f"   ❌ API状态异常: {response.status_code}")
                return False
        except Exception as e:
            print(f"   ❌ API连接失败: {e}")
            return False
    
    def test_login(self, username="admin", password="admin123"):
        """测试用户登录"""
        print("2️⃣ 测试用户登录...")
        try:
            login_data = {"username": username, "password": password}
            response = requests.post(f"{self.base_url}/api/login", json=login_data, timeout=5)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    self.token = result['data']['token']
                    user_info = result['data']
                    print(f"   ✅ 登录成功!")
                    print(f"   用户: {user_info['username']} | 角色: {user_info['role']} | 点数: {user_info['points']}")
                    return True
                else:
                    print(f"   ❌ 登录失败: {result.get('error')}")
                    return False
            else:
                print(f"   ❌ 登录请求失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"   ❌ 登录测试失败: {e}")
            return False
    
    def test_token_verification(self):
        """测试令牌验证"""
        print("3️⃣ 测试令牌验证...")
        if not self.token:
            print("   ❌ 没有有效令牌")
            return False
            
        try:
            headers = {"Authorization": f"Bearer {self.token}"}
            response = requests.post(f"{self.base_url}/api/verify_token", headers=headers, timeout=5)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print(f"   ✅ 令牌验证成功")
                    return True
                else:
                    print(f"   ❌ 令牌验证失败: {result.get('error')}")
                    return False
            else:
                print(f"   ❌ 令牌验证请求失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"   ❌ 令牌验证测试失败: {e}")
            return False
    
    def test_user_info(self, username="admin"):
        """测试获取用户信息"""
        print("4️⃣ 测试获取用户信息...")
        try:
            user_data = {"username": username}
            response = requests.post(f"{self.base_url}/api/user_info", json=user_data, timeout=5)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    user_info = result['data']
                    print(f"   ✅ 获取用户信息成功!")
                    print(f"   用户: {user_info['username']} | 角色: {user_info['role']} | 点数: {user_info['points']}")
                    return True
                else:
                    print(f"   ❌ 获取用户信息失败: {result.get('error')}")
                    return False
            else:
                print(f"   ❌ 用户信息请求失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"   ❌ 用户信息测试失败: {e}")
            return False
    
    def test_logout(self):
        """测试用户登出"""
        print("5️⃣ 测试用户登出...")
        if not self.token:
            print("   ❌ 没有有效令牌")
            return False
            
        try:
            logout_data = {"token": self.token}
            response = requests.post(f"{self.base_url}/api/logout", json=logout_data, timeout=5)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print(f"   ✅ 登出成功")
                    self.token = None
                    return True
                else:
                    print(f"   ❌ 登出失败: {result.get('error')}")
                    return False
            else:
                print(f"   ❌ 登出请求失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"   ❌ 登出测试失败: {e}")
            return False
    
    def test_error_cases(self):
        """测试错误情况"""
        print("6️⃣ 测试错误情况...")
        try:
            # 测试错误的用户名密码
            wrong_data = {"username": "wronguser", "password": "wrongpass"}
            response = requests.post(f"{self.base_url}/api/login", json=wrong_data, timeout=5)
            
            if response.status_code == 401:
                print(f"   ✅ 错误登录正确返回401")
                return True
            else:
                print(f"   ⚠️  错误登录返回状态码: {response.status_code}")
                return False
        except Exception as e:
            print(f"   ❌ 错误情况测试失败: {e}")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始API完整测试")
        print("=" * 60)
        
        tests = [
            self.test_api_status,
            self.test_login,
            self.test_token_verification,
            self.test_user_info,
            self.test_logout,
            self.test_error_cases
        ]
        
        passed = 0
        total = len(tests)
        
        for test in tests:
            if test():
                passed += 1
            print()
        
        print("=" * 60)
        print(f"测试结果: {passed}/{total} 通过")
        
        if passed == total:
            print("🎉 所有测试通过!")
            return True
        else:
            print("❌ 部分测试失败")
            return False

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='API测试工具')
    parser.add_argument('--url', default='http://localhost:5000', help='API基础URL')
    parser.add_argument('--username', default='admin', help='测试用户名')
    parser.add_argument('--password', default='admin123', help='测试密码')
    
    args = parser.parse_args()
    
    tester = APITester(args.url)
    success = tester.run_all_tests()
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
