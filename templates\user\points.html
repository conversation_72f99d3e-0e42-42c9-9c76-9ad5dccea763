{% extends "base.html" %}

{% block title %}点数详情 - 点卡平台{% endblock %}

{% block content %}
<div class="card">
    <h2>💰 点数详情</h2>
    
    <div class="stats-grid" style="margin-bottom: 2rem;">
        <div class="stat-card">
            <h3 style="color: #3498db;">{{ user.points }}</h3>
            <p>当前余额</p>
        </div>
        <div class="stat-card">
            <h3 style="color: #27ae60;">{{ recharge_records|length }}</h3>
            <p>充值次数</p>
        </div>
        <div class="stat-card">
            <h3 style="color: #e74c3c;">{{ consumption_records|length }}</h3>
            <p>消费次数</p>
        </div>
    </div>
</div>

{% if recharge_records %}
<div class="card">
    <h3>💳 充值记录</h3>
    <table class="table">
        <thead>
            <tr>
                <th>卡密</th>
                <th>充值点数</th>
                <th>充值时间</th>
            </tr>
        </thead>
        <tbody>
            {% for record in recharge_records %}
            <tr>
                <td>{{ record[0][:4] }}****{{ record[0][-4:] }}</td>
                <td style="color: #27ae60;">+{{ record[1] }}</td>
                <td>{{ record[2] }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% endif %}

{% if consumption_records %}
<div class="card">
    <h3>📊 消费记录</h3>
    <table class="table">
        <thead>
            <tr>
                <th>软件名称</th>
                <th>消费点数</th>
                <th>消费时间</th>
                <th>IP地址</th>
            </tr>
        </thead>
        <tbody>
            {% for record in consumption_records %}
            <tr>
                <td>{{ record.software_name or '未知软件' }}</td>
                <td style="color: #e74c3c;">-{{ record.points_consumed }}</td>
                <td>{{ record.timestamp }}</td>
                <td>{{ record.ip_address or '未知' }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% else %}
<div class="card">
    <h3>📊 消费记录</h3>
    <div style="text-align: center; padding: 2rem; color: #666;">
        <p>暂无消费记录</p>
    </div>
</div>
{% endif %}

<div style="text-align: center; margin-top: 2rem;">
    <a href="{{ url_for('user.dashboard') }}" class="btn btn-secondary">返回用户中心</a>
    <a href="{{ url_for('user.recharge') }}" class="btn btn-primary">立即充值</a>
</div>

<style>
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.5rem;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.stat-card h3 {
    margin: 0 0 0.5rem 0;
    font-size: 2rem;
    font-weight: bold;
    color: #ffffff;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.stat-card p {
    margin: 0;
    color: #ffffff;
    font-weight: 500;
    text-shadow: 0 1px 2px rgba(0,0,0,0.2);
    font-size: 1rem;
}
</style>
{% endblock %}
