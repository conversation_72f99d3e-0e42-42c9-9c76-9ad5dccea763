{% extends "base.html" %}

{% block title %}用户管理 - 管理后台{% endblock %}

{% block content %}
<style>
.admin-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    border-radius: 15px;
    margin-bottom: 2rem;
    text-align: center;
}

.users-table {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.table-header {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    padding: 1rem;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    margin-right: 1rem;
}

.user-info {
    display: flex;
    align-items: center;
}

.points-badge {
    background: linear-gradient(135deg, #56ab2f, #a8e6cf);
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.9rem;
}

.admin-badge {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.8rem;
}

.user-badge {
    background: linear-gradient(135deg, #4834d4, #686de0);
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.8rem;
}
</style>

<div class="admin-header">
    <h1>👥 用户管理</h1>
    <p>管理系统中的所有用户账户</p>
</div>

{% if users.items %}
<div class="users-table">
    <div class="table-header">
        <h3 style="margin: 0;">用户列表</h3>
    </div>
    
    <table class="table" style="margin: 0;">
        <thead style="background: #f8f9fa;">
            <tr>
                <th style="padding: 1rem;">用户信息</th>
                <th style="padding: 1rem;">邮箱</th>
                <th style="padding: 1rem;">点数余额</th>
                <th style="padding: 1rem;">用户类型</th>
                <th style="padding: 1rem;">注册时间</th>
                <th style="padding: 1rem;">最后登录</th>
            </tr>
        </thead>
        <tbody>
            {% for user in users.items %}
            <tr style="border-bottom: 1px solid #eee;">
                <td style="padding: 1rem;">
                    <div class="user-info">
                        <div class="user-avatar">
                            {{ user.username[0].upper() }}
                        </div>
                        <div>
                            <strong style="color: #2c3e50;">{{ user.username }}</strong>
                        </div>
                    </div>
                </td>
                <td style="padding: 1rem;">
                    <span style="color: #666;">{{ user.email }}</span>
                </td>
                <td style="padding: 1rem;">
                    <span class="points-badge">{{ user.points }} 点</span>
                </td>
                <td style="padding: 1rem;">
                    {% if user.is_admin %}
                        <span class="admin-badge">管理员</span>
                    {% else %}
                        <span class="user-badge">普通用户</span>
                    {% endif %}
                </td>
                <td style="padding: 1rem;">
                    <small style="color: #666;">{{ user.created_at.strftime('%Y-%m-%d') }}</small>
                </td>
                <td style="padding: 1rem;">
                    {% if user.last_login %}
                        <small style="color: #666;">{{ user.last_login.strftime('%Y-%m-%d %H:%M') }}</small>
                    {% else %}
                        <small style="color: #999;">从未登录</small>
                    {% endif %}
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<!-- 分页 -->
{% if users.pages > 1 %}
<div class="pagination" style="margin-top: 2rem;">
    {% if users.has_prev %}
        <a href="{{ url_for('admin.users', page=users.prev_num) }}">‹ 上一页</a>
    {% endif %}
    
    {% for page_num in users.iter_pages() %}
        {% if page_num %}
            {% if page_num != users.page %}
                <a href="{{ url_for('admin.users', page=page_num) }}">{{ page_num }}</a>
            {% else %}
                <span class="current">{{ page_num }}</span>
            {% endif %}
        {% else %}
            <span>...</span>
        {% endif %}
    {% endfor %}
    
    {% if users.has_next %}
        <a href="{{ url_for('admin.users', page=users.next_num) }}">下一页 ›</a>
    {% endif %}
</div>
{% endif %}

{% else %}
<div class="card" style="text-align: center; padding: 3rem;">
    <h3 style="color: #666; margin-bottom: 1rem;">暂无用户</h3>
    <p style="color: #999;">系统中还没有注册用户</p>
</div>
{% endif %}

<div style="text-align: center; margin-top: 2rem;">
    <a href="{{ url_for('admin.dashboard') }}" class="btn btn-secondary">返回管理后台</a>
</div>
{% endblock %}
