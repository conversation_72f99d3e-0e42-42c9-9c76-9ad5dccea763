{% extends "base.html" %}

{% block title %}创建管理员 - 超级管理员{% endblock %}

{% block content %}
<style>
.super-admin-header {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    color: white;
    padding: 2rem;
    border-radius: 15px;
    margin-bottom: 2rem;
    text-align: center;
}

.create-admin-form {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    max-width: 500px;
    margin: 0 auto;
}

.admin-type-selection {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
    margin: 1rem 0;
}

.checkbox-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 1rem 0;
}

.warning-box {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
    padding: 1rem;
    border-radius: 8px;
    margin: 1rem 0;
}
</style>

<div class="super-admin-header">
    <h1>👤 创建管理员</h1>
    <p>超级管理员专用 - 创建新的管理员账户</p>
</div>

<div class="create-admin-form">
    <h2>新建管理员账户</h2>
    
    <form method="POST">
        <div class="form-group">
            <label for="username">用户名:</label>
            <input type="text" id="username" name="username" class="form-control" required>
            <small style="color: #666;">用于登录的用户名，必须唯一</small>
        </div>
        
        <div class="form-group">
            <label for="email">邮箱:</label>
            <input type="email" id="email" name="email" class="form-control" required>
            <small style="color: #666;">管理员的邮箱地址</small>
        </div>
        
        <div class="form-group">
            <label for="password">密码:</label>
            <input type="password" id="password" name="password" class="form-control" required>
            <small style="color: #666;">密码长度至少6位</small>
        </div>
        
        <div class="form-group">
            <label for="confirm_password">确认密码:</label>
            <input type="password" id="confirm_password" name="confirm_password" class="form-control" required>
            <small style="color: #666;">请再次输入密码确认</small>
        </div>
        
        <div class="admin-type-selection">
            <h4 style="margin-bottom: 1rem; color: #2c3e50;">管理员类型</h4>
            
            <div class="checkbox-group">
                <input type="checkbox" id="is_super_admin" name="is_super_admin" value="1">
                <label for="is_super_admin"><strong>超级管理员</strong></label>
            </div>
            
            <div style="margin-left: 1.5rem; color: #666; font-size: 0.9rem;">
                <p><strong>普通管理员权限:</strong></p>
                <ul style="margin: 0.5rem 0;">
                    <li>用户管理</li>
                    <li>软件管理</li>
                    <li>卡密管理</li>
                    <li>消费记录查看</li>
                </ul>
                
                <p><strong>超级管理员额外权限:</strong></p>
                <ul style="margin: 0.5rem 0;">
                    <li>系统设置配置</li>
                    <li>创建/管理其他管理员</li>
                    <li>不受卡密面额限制</li>
                    <li>API安全策略控制</li>
                </ul>
            </div>
        </div>
        
        <div class="warning-box">
            <strong>⚠️ 重要提醒:</strong>
            <ul style="margin: 0.5rem 0 0 1rem;">
                <li>管理员账户拥有系统重要权限</li>
                <li>请设置强密码并妥善保管</li>
                <li>超级管理员权限请谨慎授予</li>
            </ul>
        </div>
        
        <button type="submit" class="btn btn-primary" style="width: 100%;">创建管理员</button>
    </form>
    
    <div style="text-align: center; margin-top: 2rem;">
        <a href="{{ url_for('admin.manage_admins') }}" class="btn btn-secondary">管理员列表</a>
        <a href="{{ url_for('admin.dashboard') }}" class="btn btn-secondary">返回管理后台</a>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const passwordInput = document.getElementById('password');
    const confirmPasswordInput = document.getElementById('confirm_password');
    
    function checkPasswordMatch() {
        if (confirmPasswordInput.value && passwordInput.value !== confirmPasswordInput.value) {
            confirmPasswordInput.setCustomValidity('密码不匹配');
        } else {
            confirmPasswordInput.setCustomValidity('');
        }
    }
    
    passwordInput.addEventListener('input', checkPasswordMatch);
    confirmPasswordInput.addEventListener('input', checkPasswordMatch);
});
</script>
{% endblock %}
