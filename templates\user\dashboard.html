{% extends "base.html" %}

{% block title %}用户中心 - 点卡平台{% endblock %}

{% block content %}
<h1>用户中心</h1>

<div class="stats-grid">
    <div class="stat-card">
        <h3>{{ user.points }}</h3>
        <p>当前点数</p>
    </div>
    <div class="stat-card">
        <h3>{{ total_consumed }}</h3>
        <p>总消费点数</p>
    </div>
    <div class="stat-card">
        <h3>{{ recent_records|length }}</h3>
        <p>最近使用次数</p>
    </div>
</div>

<div class="card">
    <h2>账户信息</h2>
    <table class="table">
        <tr>
            <td><strong>用户名:</strong></td>
            <td>{{ user.username }}</td>
        </tr>
        <tr>
            <td><strong>邮箱:</strong></td>
            <td>{{ user.email }}</td>
        </tr>
        <tr>
            <td><strong>注册时间:</strong></td>
            <td>
                {% if user.created_at %}
                    {% if user.created_at.strftime is defined %}
                        {{ user.created_at.strftime('%Y-%m-%d %H:%M:%S') }}
                    {% else %}
                        {{ user.created_at }}
                    {% endif %}
                {% else %}
                    未知
                {% endif %}
            </td>
        </tr>
        <tr>
            <td><strong>最后登录:</strong></td>
            <td>
                {% if user.last_login %}
                    {% if user.last_login.strftime is defined %}
                        {{ user.last_login.strftime('%Y-%m-%d %H:%M:%S') }}
                    {% else %}
                        {{ user.last_login }}
                    {% endif %}
                {% else %}
                    首次登录
                {% endif %}
            </td>
        </tr>
    </table>
</div>

<div class="card">
    <h2>快速操作</h2>
    <div style="display: flex; gap: 1rem; flex-wrap: wrap;">
        <a href="{{ url_for('user.points') }}" class="btn btn-info">点数详情</a>
        <a href="{{ url_for('user.recharge') }}" class="btn btn-primary">充值点数</a>
        <a href="{{ url_for('user.history') }}" class="btn btn-secondary">查看消费记录</a>
        <a href="{{ url_for('user.software_list') }}" class="btn btn-success">查看可用软件</a>
        <a href="{{ url_for('user.profile') }}" class="btn btn-warning">个人资料</a>
    </div>
</div>

{% if recent_records %}
<div class="card">
    <h2>最近消费记录</h2>
    <table class="table">
        <thead>
            <tr>
                <th>软件名称</th>
                <th>消费点数</th>
                <th>时间</th>
                <th>IP地址</th>
            </tr>
        </thead>
        <tbody>
            {% for record in recent_records %}
            <tr>
                <td>{{ record.software_name or '未知软件' }}</td>
                <td>{{ record.points_consumed }}</td>
                <td>{{ record.timestamp }}</td>
                <td>{{ record.ip_address or '未知' }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    <div style="text-align: center; margin-top: 1rem;">
        <a href="{{ url_for('user.history') }}" class="btn btn-secondary">查看全部记录</a>
    </div>
</div>
{% endif %}
{% endblock %}
