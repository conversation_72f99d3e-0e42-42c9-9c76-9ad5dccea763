#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试 deduct_points API 接口
检查数据库状态和接口调用
"""

import sys
import os
import requests
import json
import sqlite3
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from bypass_sqlalchemy import DatabaseManager

def check_database_status():
    """检查数据库状态"""
    print("🔍 检查数据库状态...")
    
    try:
        db_manager = DatabaseManager()
        
        # 检查用户表
        with db_manager.get_connection() as conn:
            cursor = conn.cursor()
            
            # 检查用户
            cursor.execute("SELECT id, username, points FROM user WHERE username = 'admin'")
            admin_user = cursor.fetchone()
            
            if admin_user:
                admin_dict = dict(admin_user)
                print(f"   ✅ 找到管理员用户: ID={admin_dict['id']}, 用户名={admin_dict['username']}, 点数={admin_dict['points']}")
            else:
                print("   ❌ 未找到管理员用户")
                return False
            
            # 检查软件表
            cursor.execute("SELECT id, name, api_key, points_per_use, is_active FROM software LIMIT 1")
            software = cursor.fetchone()
            
            if software:
                software_dict = dict(software)
                print(f"   ✅ 找到软件: ID={software_dict['id']}, 名称={software_dict['name']}, API密钥={software_dict['api_key'][:10]}..., 激活={software_dict['is_active']}")
                return admin_dict, software_dict
            else:
                print("   ❌ 未找到软件记录")
                return False
                
    except Exception as e:
        print(f"   ❌ 数据库检查失败: {e}")
        return False

def check_consumption_record_table():
    """检查消费记录表结构"""
    print("🔍 检查消费记录表结构...")
    
    try:
        db_manager = DatabaseManager()
        
        with db_manager.get_connection() as conn:
            cursor = conn.cursor()
            
            # 检查表结构
            cursor.execute("PRAGMA table_info(consumption_record)")
            columns = cursor.fetchall()
            
            print("   📋 consumption_record 表结构:")
            for col in columns:
                col_dict = dict(col)
                print(f"      - {col_dict['name']}: {col_dict['type']} (NOT NULL: {col_dict['notnull']})")
            
            # 检查是否有记录
            cursor.execute("SELECT COUNT(*) as count FROM consumption_record")
            count_result = cursor.fetchone()
            count = dict(count_result)['count']
            print(f"   📊 消费记录数量: {count}")
            
            return True
            
    except Exception as e:
        print(f"   ❌ 消费记录表检查失败: {e}")
        return False

def test_deduct_points_directly():
    """直接测试数据库扣费方法"""
    print("🔍 直接测试数据库扣费方法...")
    
    try:
        db_manager = DatabaseManager()
        
        # 获取测试用户
        admin_user = db_manager.get_user_by_username('admin')
        if not admin_user:
            print("   ❌ 未找到管理员用户")
            return False
        
        print(f"   📋 扣费前用户点数: {admin_user['points']}")
        
        # 获取软件信息
        with db_manager.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM software WHERE is_active = 1 LIMIT 1")
            software_row = cursor.fetchone()
            
            if not software_row:
                print("   ❌ 未找到激活的软件")
                return False
            
            software = dict(software_row)
            print(f"   📋 使用软件: {software['name']} (ID: {software['id']})")
        
        # 尝试扣费
        try:
            record_id = db_manager.deduct_user_points(
                user_id=admin_user['id'],
                points=1,
                software_id=software['id'],
                ip_address='127.0.0.1',
                user_agent='Debug Script'
            )
            
            print(f"   ✅ 扣费成功，记录ID: {record_id}")
            
            # 检查扣费后的点数
            updated_user = db_manager.get_user_by_id(admin_user['id'])
            print(f"   📋 扣费后用户点数: {updated_user['points']}")
            
            return True
            
        except Exception as e:
            print(f"   ❌ 扣费失败: {e}")
            import traceback
            traceback.print_exc()
            return False
            
    except Exception as e:
        print(f"   ❌ 直接测试失败: {e}")
        return False

def test_api_deduct_points():
    """测试API接口"""
    print("🔍 测试API接口...")
    
    # 首先获取API密钥
    try:
        db_manager = DatabaseManager()
        
        with db_manager.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT api_key FROM software WHERE is_active = 1 LIMIT 1")
            software_row = cursor.fetchone()
            
            if not software_row:
                print("   ❌ 未找到激活的软件")
                return False
            
            api_key = dict(software_row)['api_key']
            print(f"   📋 使用API密钥: {api_key[:10]}...")
        
        # 测试API调用
        url = "http://localhost:5000/api/deduct_points"
        headers = {
            "Content-Type": "application/json",
            "X-API-Key": api_key
        }
        data = {
            "username": "admin",
            "points": 1
        }
        
        print(f"   📤 发送请求: {url}")
        print(f"   📤 请求数据: {data}")
        
        response = requests.post(url, json=data, headers=headers, timeout=10)
        
        print(f"   📥 响应状态码: {response.status_code}")
        print(f"   📥 响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("   ✅ API调用成功")
                return True
            else:
                print(f"   ❌ API调用失败: {result.get('error')}")
                return False
        else:
            print(f"   ❌ API返回错误状态码: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ API测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 开始调试 deduct_points API 接口")
    print("=" * 60)
    
    # 1. 检查数据库状态
    db_status = check_database_status()
    if not db_status:
        print("❌ 数据库状态检查失败，无法继续")
        return False
    
    # 2. 检查消费记录表
    if not check_consumption_record_table():
        print("❌ 消费记录表检查失败")
        return False
    
    # 3. 直接测试数据库方法
    if not test_deduct_points_directly():
        print("❌ 数据库方法测试失败")
        return False
    
    # 4. 测试API接口
    if not test_api_deduct_points():
        print("❌ API接口测试失败")
        return False
    
    print("\n✅ 所有测试通过！")
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n🎉 调试完成！")
            sys.exit(0)
        else:
            print("\n❌ 调试发现问题！")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⚠️  调试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 调试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
