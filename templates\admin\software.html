{% extends "base.html" %}

{% block title %}软件管理 - 管理后台{% endblock %}

{% block content %}
<h1>软件管理</h1>

<div class="card">
    <h2>添加新软件</h2>
    <form method="POST">
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
            <div class="form-group">
                <label for="name">软件名称:</label>
                <input type="text" id="name" name="name" class="form-control" required>
            </div>
            
            <div class="form-group">
                <label for="points_per_use">每次使用扣费点数:</label>
                <input type="number" id="points_per_use" name="points_per_use" class="form-control" min="1" required>
            </div>
        </div>
        
        <div class="form-group">
            <label for="description">软件描述:</label>
            <textarea id="description" name="description" class="form-control" rows="3" placeholder="可选"></textarea>
        </div>
        
        <button type="submit" class="btn btn-primary">添加软件</button>
    </form>
</div>

{% if software_list %}
<div class="card">
    <h2>软件列表</h2>
    <table class="table">
        <thead>
            <tr>
                <th>软件名称</th>
                <th>描述</th>
                <th>扣费点数</th>
                <th>API密钥</th>
                <th>状态</th>
                <th>创建时间</th>
                <th>操作</th>
            </tr>
        </thead>
        <tbody>
            {% for software in software_list %}
            <tr>
                <td><strong>{{ software.name }}</strong></td>
                <td>{{ software.description or '无' }}</td>
                <td>{{ software.points_per_use }}</td>
                <td>
                    <code style="font-size: 0.8rem;">{{ software.api_key[:20] }}...</code>
                    <button onclick="copyToClipboard('{{ software.api_key }}')" class="btn btn-secondary" style="padding: 2px 8px; font-size: 0.8rem;">复制</button>
                </td>
                <td>
                    {% if software.is_active %}
                        <span style="color: #27ae60;">✓ 启用</span>
                    {% else %}
                        <span style="color: #e74c3c;">✗ 禁用</span>
                    {% endif %}
                </td>
                <td>{{ software.created_at.strftime('%Y-%m-%d') if software.created_at and software.created_at.strftime else (software.created_at or '未知') }}</td>
                <td>
                    {% if current_user.is_super_admin %}
                    <a href="{{ url_for('admin.edit_software', software_id=software.id) }}"
                       class="btn btn-primary" style="padding: 4px 8px; font-size: 0.8rem;">
                        编辑
                    </a>
                    {% endif %}
                    <a href="{{ url_for('admin.toggle_software', software_id=software.id) }}"
                       class="btn btn-warning" style="padding: 4px 8px; font-size: 0.8rem;">
                        {{ '禁用' if software.is_active else '启用' }}
                    </a>
                    <a href="{{ url_for('admin.regenerate_api_key', software_id=software.id) }}"
                       class="btn btn-secondary" style="padding: 4px 8px; font-size: 0.8rem;"
                       data-confirm="确定要重新生成API密钥吗？旧密钥将失效。">
                        重新生成密钥
                    </a>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% else %}
<div class="card">
    <div style="text-align: center; padding: 2rem; color: #666;">
        <p>暂无软件，请先添加软件</p>
    </div>
</div>
{% endif %}

<div style="text-align: center; margin-top: 1rem;">
    <a href="{{ url_for('admin.dashboard') }}" class="btn btn-secondary">返回管理后台</a>
</div>
{% endblock %}
