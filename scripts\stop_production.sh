#!/bin/bash
# 生产环境停止脚本

echo "停止点卡平台生产环境..."

# 检查PID文件
if [ ! -f "gunicorn.pid" ]; then
    echo "未找到PID文件，服务可能未运行"
    exit 1
fi

# 读取PID
PID=$(cat gunicorn.pid)

# 检查进程是否存在
if ! ps -p $PID > /dev/null 2>&1; then
    echo "进程 $PID 不存在，清理PID文件"
    rm -f gunicorn.pid
    exit 1
fi

# 停止进程
echo "停止进程 $PID..."
kill $PID

# 等待进程结束
sleep 2

# 检查是否成功停止
if ps -p $PID > /dev/null 2>&1; then
    echo "强制停止进程..."
    kill -9 $PID
    sleep 1
fi

# 清理PID文件
rm -f gunicorn.pid

echo "服务已停止"
