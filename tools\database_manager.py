#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库管理工具
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from bypass_sqlalchemy import db_manager
import sqlite3
import shutil
from datetime import datetime

def backup_database(backup_path=None):
    """备份数据库"""
    if not backup_path:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = f"point_card_platform.db.backup_{timestamp}"
    
    try:
        shutil.copy2("point_card_platform.db", backup_path)
        print(f"✅ 数据库备份成功: {backup_path}")
        return backup_path
    except Exception as e:
        print(f"❌ 数据库备份失败: {e}")
        return None

def restore_database(backup_path):
    """恢复数据库"""
    try:
        if not os.path.exists(backup_path):
            print(f"❌ 备份文件不存在: {backup_path}")
            return False
        
        # 备份当前数据库
        current_backup = backup_database()
        if not current_backup:
            print("❌ 无法备份当前数据库")
            return False
        
        # 恢复数据库
        shutil.copy2(backup_path, "point_card_platform.db")
        print(f"✅ 数据库恢复成功")
        print(f"   当前数据库已备份为: {current_backup}")
        return True
    except Exception as e:
        print(f"❌ 数据库恢复失败: {e}")
        return False

def show_database_info():
    """显示数据库信息"""
    print("📊 数据库信息")
    print("=" * 50)
    
    try:
        with db_manager.get_connection() as conn:
            cursor = conn.cursor()
            
            # 获取表信息
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            
            print(f"数据库文件: point_card_platform.db")
            print(f"表数量: {len(tables)}")
            print()
            
            for table in tables:
                table_name = table[0]
                print(f"📋 表: {table_name}")
                
                # 获取表结构
                cursor.execute(f"PRAGMA table_info({table_name})")
                columns = cursor.fetchall()
                print("   字段:")
                for col in columns:
                    print(f"     {col[1]} ({col[2]}) {'NOT NULL' if col[3] else 'NULL'}")
                
                # 获取记录数
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = cursor.fetchone()[0]
                print(f"   记录数: {count}")
                print()
                
    except Exception as e:
        print(f"❌ 获取数据库信息失败: {e}")

def clean_database():
    """清理数据库"""
    print("🧹 清理数据库")
    print("=" * 50)
    
    try:
        with db_manager.get_connection() as conn:
            cursor = conn.cursor()
            
            # 清理过期的API令牌
            cursor.execute("DELETE FROM api_tokens WHERE expires_at < datetime('now')")
            deleted_tokens = cursor.rowcount
            
            # 清理旧的API日志 (保留最近30天)
            cursor.execute("DELETE FROM api_logs WHERE created_at < datetime('now', '-30 days')")
            deleted_logs = cursor.rowcount
            
            conn.commit()
            
            print(f"✅ 清理完成")
            print(f"   删除过期令牌: {deleted_tokens}")
            print(f"   删除旧日志: {deleted_logs}")
            
    except Exception as e:
        print(f"❌ 清理数据库失败: {e}")

def reset_database():
    """重置数据库"""
    print("⚠️  重置数据库")
    print("=" * 50)
    print("这将删除所有数据并重新初始化数据库!")
    
    confirm = input("请输入 'RESET' 确认重置: ").strip()
    if confirm != "RESET":
        print("❌ 操作已取消")
        return False
    
    try:
        # 备份当前数据库
        backup_path = backup_database()
        if not backup_path:
            print("❌ 无法备份当前数据库")
            return False
        
        # 删除数据库文件
        if os.path.exists("point_card_platform.db"):
            os.remove("point_card_platform.db")
        
        # 重新初始化数据库
        db_manager.init_database()
        
        print(f"✅ 数据库重置成功")
        print(f"   原数据库已备份为: {backup_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 重置数据库失败: {e}")
        return False

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='数据库管理工具')
    parser.add_argument('--backup', metavar='PATH', help='备份数据库到指定路径')
    parser.add_argument('--restore', metavar='PATH', help='从指定路径恢复数据库')
    parser.add_argument('--info', action='store_true', help='显示数据库信息')
    parser.add_argument('--clean', action='store_true', help='清理数据库')
    parser.add_argument('--reset', action='store_true', help='重置数据库')
    
    args = parser.parse_args()
    
    if args.backup:
        backup_database(args.backup)
    elif args.restore:
        restore_database(args.restore)
    elif args.info:
        show_database_info()
    elif args.clean:
        clean_database()
    elif args.reset:
        reset_database()
    else:
        parser.print_help()

if __name__ == "__main__":
    main()
