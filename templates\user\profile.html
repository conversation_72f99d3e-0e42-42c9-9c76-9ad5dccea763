{% extends "base.html" %}

{% block title %}个人资料 - 点卡平台{% endblock %}

{% block content %}
<div class="profile-container">
    <div class="profile-header">
        <div class="profile-avatar">
            {{ user.username[0].upper() }}
        </div>
        <div class="profile-info">
            <h2>{{ user.username }}</h2>
            <p>{{ user.email }}</p>
            <span class="user-role">
                {% if user.is_super_admin %}
                    超级管理员
                {% elif user.is_admin %}
                    管理员
                {% else %}
                    普通用户
                {% endif %}
            </span>
        </div>
    </div>

    <div class="profile-stats">
        <div class="stat-item">
            <div class="stat-value">{{ user.points }}</div>
            <div class="stat-label">当前点数</div>
        </div>
        <div class="stat-item">
            <div class="stat-value">
                {% if user.created_at %}
                    {{ user.created_at[:10] if user.created_at is string else user.created_at.strftime('%Y-%m-%d') }}
                {% else %}
                    未知
                {% endif %}
            </div>
            <div class="stat-label">注册日期</div>
        </div>
        <div class="stat-item">
            <div class="stat-value">
                {% if user.last_login %}
                    {{ user.last_login[:10] if user.last_login is string else user.last_login.strftime('%Y-%m-%d') }}
                {% else %}
                    首次登录
                {% endif %}
            </div>
            <div class="stat-label">最后登录</div>
        </div>
    </div>

    <div class="card">
        <h3>📝 个人信息</h3>
        <form method="POST" class="profile-form">
            <div class="form-group">
                <label for="username">用户名:</label>
                <input type="text" id="username" name="username" class="form-control" 
                       value="{{ user.username }}" readonly>
                <small class="form-text">用户名不可修改</small>
            </div>

            <div class="form-group">
                <label for="email">邮箱地址:</label>
                <input type="email" id="email" name="email" class="form-control" 
                       value="{{ user.email }}" required>
                <small class="form-text">用于接收重要通知</small>
            </div>

            <div class="form-actions">
                <button type="submit" class="btn btn-primary">保存修改</button>
                <a href="{{ url_for('user.dashboard') }}" class="btn btn-secondary">取消</a>
            </div>
        </form>
    </div>

    <div class="card">
        <h3>🔐 安全设置</h3>
        <div class="security-actions">
            <a href="{{ url_for('auth.change_password') }}" class="btn btn-warning">
                🔑 修改密码
            </a>
            <div class="security-info">
                <p>定期修改密码可以提高账户安全性</p>
            </div>
        </div>
    </div>

    <div class="card">
        <h3>📊 账户统计</h3>
        <div class="account-stats">
            <div class="stats-grid">
                <div class="stats-item">
                    <span class="stats-label">账户状态:</span>
                    <span class="stats-value status-active">正常</span>
                </div>
                <div class="stats-item">
                    <span class="stats-label">用户类型:</span>
                    <span class="stats-value">
                        {% if user.is_super_admin %}
                            超级管理员
                        {% elif user.is_admin %}
                            管理员
                        {% else %}
                            普通用户
                        {% endif %}
                    </span>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.profile-container {
    max-width: 800px;
    margin: 0 auto;
}

.profile-header {
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    border-radius: 10px;
    margin-bottom: 2rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.profile-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: rgba(255,255,255,0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    font-weight: bold;
    margin-right: 2rem;
}

.profile-info h2 {
    margin: 0 0 0.5rem 0;
    font-size: 1.8rem;
}

.profile-info p {
    margin: 0 0 0.5rem 0;
    opacity: 0.9;
}

.user-role {
    background: rgba(255,255,255,0.2);
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.875rem;
}

.profile-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.stat-item {
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.stat-value {
    font-size: 1.5rem;
    font-weight: bold;
    color: #3498db;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #666;
    font-size: 0.9rem;
}

.profile-form .form-group {
    margin-bottom: 1.5rem;
}

.form-actions {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
}

.security-actions {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.security-info p {
    margin: 0;
    color: #666;
    font-size: 0.9rem;
}

.account-stats {
    padding: 1rem 0;
}

.stats-grid {
    display: grid;
    gap: 1rem;
}

.stats-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #eee;
}

.stats-item:last-child {
    border-bottom: none;
}

.stats-label {
    font-weight: 500;
    color: #333;
}

.stats-value {
    color: #666;
}

.status-active {
    color: #27ae60 !important;
    font-weight: bold;
}

.form-text {
    color: #666;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}
</style>
{% endblock %}
