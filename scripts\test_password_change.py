#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试密码修改功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_simple_user_class():
    """测试SimpleUser类的密码验证功能"""
    print("🧪 测试SimpleUser类...")
    
    try:
        from bypass_sqlalchemy import DatabaseManager, SimpleUser
        db_manager = DatabaseManager()
        
        # 获取测试用户
        user_data = db_manager.get_user_by_username('testuser')
        if not user_data:
            print("   ❌ 测试用户不存在")
            return False
        
        # 创建SimpleUser实例
        user = SimpleUser(user_data)
        
        # 检查必要的属性
        required_attrs = ['id', 'username', 'email', 'password_hash', 'is_admin', 'is_super_admin', 'points']
        for attr in required_attrs:
            if not hasattr(user, attr):
                print(f"   ❌ SimpleUser缺少属性: {attr}")
                return False
        
        print(f"   ✅ SimpleUser属性完整")
        print(f"     用户ID: {user.id}")
        print(f"     用户名: {user.username}")
        print(f"     邮箱: {user.email}")
        print(f"     是否管理员: {user.is_admin}")
        
        # 检查check_password方法
        if not hasattr(user, 'check_password'):
            print("   ❌ SimpleUser缺少check_password方法")
            return False
        
        # 测试密码验证
        correct_password = 'test123'  # 默认测试用户密码
        wrong_password = 'wrongpassword'
        
        if user.check_password(correct_password):
            print("   ✅ 正确密码验证成功")
        else:
            print("   ❌ 正确密码验证失败")
            return False
        
        if not user.check_password(wrong_password):
            print("   ✅ 错误密码验证正确拒绝")
        else:
            print("   ❌ 错误密码验证失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ 测试SimpleUser类失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_password_hashing():
    """测试密码哈希功能"""
    print("🔐 测试密码哈希功能...")
    
    try:
        from bypass_sqlalchemy import DatabaseManager
        db_manager = DatabaseManager()
        
        test_password = "testpassword123"
        
        # 测试hash_password方法
        hashed = db_manager.hash_password(test_password)
        print(f"   原密码: {test_password}")
        print(f"   哈希值: {hashed[:20]}...")
        
        # 测试verify_password方法
        if db_manager.verify_password(test_password, hashed):
            print("   ✅ 密码哈希和验证功能正常")
        else:
            print("   ❌ 密码哈希和验证功能异常")
            return False
        
        # 测试错误密码
        if not db_manager.verify_password("wrongpassword", hashed):
            print("   ✅ 错误密码正确拒绝")
        else:
            print("   ❌ 错误密码验证失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ 测试密码哈希失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_connection():
    """测试数据库连接"""
    print("🗄️  测试数据库连接...")
    
    try:
        from bypass_sqlalchemy import DatabaseManager
        db_manager = DatabaseManager()
        
        with db_manager.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM user")
            user_count = cursor.fetchone()[0]
            print(f"   ✅ 数据库连接正常，用户数量: {user_count}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 数据库连接失败: {e}")
        return False

def test_user_retrieval():
    """测试用户获取功能"""
    print("👤 测试用户获取功能...")
    
    try:
        from bypass_sqlalchemy import DatabaseManager
        db_manager = DatabaseManager()
        
        # 测试按用户名获取用户
        user = db_manager.get_user_by_username('testuser')
        if user:
            print(f"   ✅ 按用户名获取用户成功: {user['username']}")
            
            # 检查必要字段
            required_fields = ['id', 'username', 'email', 'password_hash', 'is_admin', 'is_super_admin', 'points']
            for field in required_fields:
                if field not in user:
                    print(f"   ❌ 用户数据缺少字段: {field}")
                    return False
            
            print("   ✅ 用户数据字段完整")
        else:
            print("   ❌ 获取测试用户失败")
            return False
        
        # 测试按ID获取用户
        user_by_id = db_manager.get_user_by_id(user['id'])
        if user_by_id:
            print(f"   ✅ 按ID获取用户成功: {user_by_id['username']}")
        else:
            print("   ❌ 按ID获取用户失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ 测试用户获取失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def simulate_password_change():
    """模拟密码修改过程"""
    print("🔄 模拟密码修改过程...")
    
    try:
        from bypass_sqlalchemy import DatabaseManager, SimpleUser
        db_manager = DatabaseManager()
        
        # 获取测试用户
        user_data = db_manager.get_user_by_username('testuser')
        if not user_data:
            print("   ❌ 测试用户不存在")
            return False
        
        user = SimpleUser(user_data)
        
        # 模拟密码修改流程
        current_password = 'test123'
        new_password = 'newtest123'
        
        # 1. 验证当前密码
        if not user.check_password(current_password):
            print("   ❌ 当前密码验证失败")
            return False
        print("   ✅ 当前密码验证成功")
        
        # 2. 生成新密码哈希
        new_password_hash = db_manager.hash_password(new_password)
        print("   ✅ 新密码哈希生成成功")
        
        # 3. 模拟更新数据库（不实际更新）
        print("   ✅ 密码修改流程模拟成功")
        
        # 4. 验证新密码哈希
        if db_manager.verify_password(new_password, new_password_hash):
            print("   ✅ 新密码哈希验证成功")
        else:
            print("   ❌ 新密码哈希验证失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ 模拟密码修改失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 开始测试密码修改功能")
    print("=" * 60)
    
    tests = [
        ("数据库连接", test_database_connection),
        ("用户获取功能", test_user_retrieval),
        ("密码哈希功能", test_password_hashing),
        ("SimpleUser类", test_simple_user_class),
        ("密码修改流程", simulate_password_change),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}:")
        if test_func():
            passed += 1
            print(f"   ✅ {test_name} 测试通过")
        else:
            print(f"   ❌ {test_name} 测试失败")
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！密码修改功能应该正常工作。")
        return True
    else:
        print("❌ 部分测试失败，需要进一步检查。")
        return False

if __name__ == "__main__":
    if main():
        print("\n✅ 测试完成！可以尝试修改密码功能。")
    else:
        print("\n❌ 测试失败！请检查错误信息。")
        sys.exit(1)
