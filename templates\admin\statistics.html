{% extends "base.html" %}

{% block title %}统计报表 - 管理后台{% endblock %}

{% block content %}
<style>
.stats-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    border-radius: 15px;
    margin-bottom: 2rem;
    text-align: center;
}

.chart-container {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.stats-table {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.table-header {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    padding: 1rem;
}

.software-bar {
    height: 20px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 10px;
    margin: 5px 0;
    position: relative;
}

.bar-label {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: white;
    font-size: 0.8rem;
    font-weight: 600;
}

.daily-chart {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(40px, 1fr));
    gap: 5px;
    margin-top: 1rem;
}

.daily-bar {
    background: linear-gradient(to top, #667eea, #764ba2);
    border-radius: 4px 4px 0 0;
    min-height: 20px;
    position: relative;
    transition: all 0.3s ease;
}

.daily-bar:hover {
    transform: scale(1.1);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.daily-label {
    font-size: 0.7rem;
    text-align: center;
    margin-top: 5px;
    color: #666;
}

.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.summary-card {
    background: white;
    padding: 1.5rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    text-align: center;
    border-left: 4px solid #667eea;
}

.summary-number {
    font-size: 2rem;
    font-weight: 800;
    color: #667eea;
    margin-bottom: 0.5rem;
}

.summary-label {
    color: #666;
    font-weight: 500;
}
</style>

<div class="stats-header">
    <h1>📊 统计报表</h1>
    <p>系统数据分析和使用统计</p>
</div>

<!-- 总体统计 -->
<div class="summary-grid">
    <div class="summary-card">
        <div class="summary-number">{{ software_stats|length }}</div>
        <div class="summary-label">活跃软件数</div>
    </div>
    <div class="summary-card">
        <div class="summary-number">
            {{ software_stats|sum(attribute='total_consumed') or 0 }}
        </div>
        <div class="summary-label">总消费点数</div>
    </div>
    <div class="summary-card">
        <div class="summary-number">
            {{ software_stats|sum(attribute='usage_count') or 0 }}
        </div>
        <div class="summary-label">总使用次数</div>
    </div>
    <div class="summary-card">
        <div class="summary-number">{{ daily_stats|length }}</div>
        <div class="summary-label">活跃天数</div>
    </div>
</div>

<!-- 软件使用统计 -->
{% if software_stats %}
<div class="chart-container">
    <h3 style="color: #2c3e50; margin-bottom: 1.5rem;">🔧 软件使用统计</h3>
    
    {% set max_consumed = software_stats|max(attribute='total_consumed') %}
    {% for stat in software_stats %}
    <div style="margin-bottom: 1rem;">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;">
            <strong style="color: #2c3e50;">{{ stat.name }}</strong>
            <span style="color: #666; font-size: 0.9rem;">
                {{ stat.total_consumed }} 点 ({{ stat.usage_count }} 次)
            </span>
        </div>
        <div style="background: #f0f0f0; border-radius: 10px; overflow: hidden;">
            <div class="software-bar" style="width: {{ (stat.total_consumed / max_consumed.total_consumed * 100) if max_consumed.total_consumed > 0 else 0 }}%;">
                <span class="bar-label">{{ "%.1f"|format((stat.total_consumed / max_consumed.total_consumed * 100) if max_consumed.total_consumed > 0 else 0) }}%</span>
            </div>
        </div>
    </div>
    {% endfor %}
</div>
{% endif %}

<!-- 每日消费趋势 -->
{% if daily_stats %}
<div class="chart-container">
    <h3 style="color: #2c3e50; margin-bottom: 1.5rem;">📈 每日消费趋势 (最近30天)</h3>
    
    {% set max_daily = daily_stats|max(attribute='total_consumed') %}
    <div class="daily-chart">
        {% for stat in daily_stats|reverse %}
        <div style="text-align: center;">
            <div class="daily-bar" 
                 style="height: {{ (stat.total_consumed / max_daily.total_consumed * 100 + 20) if max_daily.total_consumed > 0 else 20 }}px;"
                 title="{{ stat.date }}: {{ stat.total_consumed }} 点">
            </div>
            <div class="daily-label">{{ stat.date.strftime('%m/%d') if stat.date else 'N/A' }}</div>
        </div>
        {% endfor %}
    </div>
    
    <div style="margin-top: 1rem; text-align: center; color: #666; font-size: 0.9rem;">
        悬停查看详细数据 | 最高: {{ max_daily.total_consumed if max_daily else 0 }} 点
    </div>
</div>
{% endif %}

<!-- 详细数据表 -->
{% if software_stats %}
<div class="stats-table">
    <div class="table-header">
        <h3 style="margin: 0;">软件详细统计</h3>
    </div>
    
    <table class="table" style="margin: 0;">
        <thead style="background: #f8f9fa;">
            <tr>
                <th style="padding: 1rem;">软件名称</th>
                <th style="padding: 1rem;">总消费点数</th>
                <th style="padding: 1rem;">使用次数</th>
                <th style="padding: 1rem;">平均每次消费</th>
                <th style="padding: 1rem;">使用占比</th>
            </tr>
        </thead>
        <tbody>
            {% set total_usage = software_stats|sum(attribute='usage_count') %}
            {% for stat in software_stats %}
            <tr style="border-bottom: 1px solid #eee;">
                <td style="padding: 1rem;">
                    <strong style="color: #2c3e50;">{{ stat.name }}</strong>
                </td>
                <td style="padding: 1rem;">
                    <span style="color: #667eea; font-weight: 600;">{{ stat.total_consumed }}</span> 点
                </td>
                <td style="padding: 1rem;">
                    <span style="color: #27ae60; font-weight: 600;">{{ stat.usage_count }}</span> 次
                </td>
                <td style="padding: 1rem;">
                    <span style="color: #f39c12; font-weight: 600;">
                        {{ "%.1f"|format(stat.total_consumed / stat.usage_count if stat.usage_count > 0 else 0) }}
                    </span> 点/次
                </td>
                <td style="padding: 1rem;">
                    <span style="color: #e74c3c; font-weight: 600;">
                        {{ "%.1f"|format((stat.usage_count / total_usage * 100) if total_usage > 0 else 0) }}%
                    </span>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% endif %}

{% if not software_stats and not daily_stats %}
<div class="chart-container" style="text-align: center; padding: 3rem;">
    <h3 style="color: #666; margin-bottom: 1rem;">暂无统计数据</h3>
    <p style="color: #999; margin-bottom: 2rem;">系统中还没有任何使用记录</p>
    <div style="font-size: 4rem; margin: 2rem 0; opacity: 0.3;">📊</div>
    <a href="{{ url_for('admin.software') }}" class="btn btn-primary">添加软件</a>
</div>
{% endif %}

<div style="text-align: center; margin-top: 2rem;">
    <a href="{{ url_for('admin.dashboard') }}" class="btn btn-secondary">返回管理后台</a>
</div>

<script>
// 简单的图表交互
document.addEventListener('DOMContentLoaded', function() {
    const dailyBars = document.querySelectorAll('.daily-bar');
    dailyBars.forEach(bar => {
        bar.addEventListener('mouseenter', function() {
            this.style.opacity = '0.8';
        });
        bar.addEventListener('mouseleave', function() {
            this.style.opacity = '1';
        });
    });
});
</script>
{% endblock %}
