#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试消费记录功能
1. 测试IP地址显示问题
2. 测试消费记录页面模板
3. 创建测试数据
"""

import sys
import os
import sqlite3
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_ip_address_display():
    """测试IP地址显示"""
    print("🌐 测试IP地址显示...")
    
    try:
        from bypass_sqlalchemy import DatabaseManager
        
        db_manager = DatabaseManager()
        
        # 获取最近的消费记录
        recent_records = db_manager.get_recent_consumption_records(5)
        
        print(f"   📋 获取到 {len(recent_records)} 条最近消费记录")
        
        for i, record in enumerate(recent_records):
            print(f"   📋 记录{i+1}:")
            print(f"      用户: {record.get('user', {}).get('username', '未知')}")
            print(f"      软件: {record.get('software', {}).get('name', '未知')}")
            print(f"      点数: {record.get('points_consumed', 0)}")
            print(f"      时间: {record.get('timestamp', '未知')}")
            print(f"      IP地址: {record.get('ip_address', '未知')}")
            print(f"      用户代理: {record.get('user_agent', '未知')}")
            print()
        
        # 检查是否有IP地址信息
        has_ip = any(record.get('ip_address') for record in recent_records)
        if has_ip:
            print("   ✅ IP地址显示测试通过")
            return True
        else:
            print("   ⚠️  没有找到IP地址信息，可能需要创建测试数据")
            return True  # 这不算错误，只是没有数据
            
    except Exception as e:
        print(f"   ❌ 测试IP地址显示失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_consumption_records_pagination():
    """测试消费记录分页功能"""
    print("📄 测试消费记录分页功能...")
    
    try:
        from bypass_sqlalchemy import DatabaseManager
        
        db_manager = DatabaseManager()
        
        # 测试分页查询
        records_data = db_manager.get_consumption_records_paginated(0, 10)
        total_records = db_manager.get_consumption_record_count()
        
        print(f"   📋 总记录数: {total_records}")
        print(f"   📋 第一页记录数: {len(records_data)}")
        
        if records_data:
            print("   📋 第一条记录详情:")
            record = records_data[0]
            print(f"      ID: {record.get('id')}")
            print(f"      用户: {record.get('username', '未知')}")
            print(f"      软件: {record.get('software_name', '未知')}")
            print(f"      点数: {record.get('points_consumed', 0)}")
            print(f"      时间: {record.get('timestamp', '未知')}")
            print(f"      IP地址: {record.get('ip_address', '未知')}")
            print(f"      用户代理: {record.get('user_agent', '未知')}")
        
        print("   ✅ 消费记录分页功能测试通过")
        return True
        
    except Exception as e:
        print(f"   ❌ 测试消费记录分页功能失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_test_consumption_records():
    """创建测试消费记录（包含IP地址）"""
    print("📝 创建测试消费记录...")
    
    try:
        conn = sqlite3.connect('dianka.db')
        cursor = conn.cursor()
        
        # 检查是否已有消费记录
        cursor.execute("SELECT COUNT(*) FROM consumption_record")
        count = cursor.fetchone()[0]
        
        if count >= 5:
            print(f"   ℹ️  已有 {count} 条消费记录，跳过创建")
            conn.close()
            return True
        
        # 获取用户和软件ID
        cursor.execute("SELECT id, username FROM user LIMIT 3")
        users = cursor.fetchall()
        if not users:
            print("   ❌ 未找到用户")
            conn.close()
            return False
        
        cursor.execute("SELECT id, name FROM software LIMIT 2")
        software_list = cursor.fetchall()
        if not software_list:
            print("   ❌ 未找到软件")
            conn.close()
            return False
        
        # 创建测试消费记录
        test_records = []
        test_ips = ['*************', '*********', '***********', '************', '************']
        test_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36',
            'PostmanRuntime/7.29.0',
            'Python-requests/2.28.1'
        ]
        
        for i in range(5):
            user = users[i % len(users)]
            software = software_list[i % len(software_list)]
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            test_records.append((
                user[0],  # user_id
                software[0],  # software_id
                10 + i,  # points_consumed
                test_ips[i],  # ip_address
                test_agents[i],  # user_agent
                timestamp  # timestamp
            ))
        
        cursor.executemany("""
            INSERT INTO consumption_record (user_id, software_id, points_consumed, ip_address, user_agent, timestamp)
            VALUES (?, ?, ?, ?, ?, ?)
        """, test_records)
        
        conn.commit()
        conn.close()
        
        print(f"   ✅ 创建了 {len(test_records)} 条测试消费记录")
        print("   📋 测试记录详情:")
        for i, record in enumerate(test_records):
            print(f"      记录{i+1}: 用户ID={record[0]}, 软件ID={record[1]}, 点数={record[2]}, IP={record[3]}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 创建测试消费记录失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_consumption_record_table():
    """检查消费记录表结构"""
    print("🔍 检查消费记录表结构...")
    
    try:
        conn = sqlite3.connect('dianka.db')
        cursor = conn.cursor()
        
        # 检查表结构
        cursor.execute("PRAGMA table_info(consumption_record)")
        columns = cursor.fetchall()
        
        print("   📋 consumption_record 表结构:")
        for col in columns:
            print(f"      {col[1]} ({col[2]}) - {'NOT NULL' if col[3] else 'NULL'}")
        
        # 检查是否有必要的字段
        column_names = [col[1] for col in columns]
        required_fields = ['id', 'user_id', 'software_id', 'points_consumed', 'ip_address', 'user_agent', 'timestamp']
        
        missing_fields = [field for field in required_fields if field not in column_names]
        if missing_fields:
            print(f"   ❌ 缺少字段: {missing_fields}")
            conn.close()
            return False
        else:
            print("   ✅ 表结构检查通过")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"   ❌ 检查表结构失败: {e}")
        return False

def show_usage_instructions():
    """显示使用说明"""
    print("\n📖 功能使用说明")
    print("=" * 60)
    print("🌐 IP地址显示修复:")
    print("   1. 最近消费记录现在会正确显示IP地址")
    print("   2. 如果显示'未知'，说明该记录创建时没有记录IP地址")
    print("   3. 新的API调用会正确记录IP地址")
    print("\n📄 消费记录管理页面:")
    print("   1. 使用管理员或超级管理员账号登录")
    print("   2. 访问管理后台 -> 点击'查看全部记录'")
    print("   3. 或直接访问 /admin/consumption_records")
    print("   4. 支持分页浏览，每页显示50条记录")
    print("   5. 显示详细信息：用户、软件、点数、时间、IP、用户代理")

def main():
    """主函数"""
    print("🚀 开始测试消费记录功能")
    print("=" * 60)
    
    results = []
    
    # 1. 检查表结构
    results.append(("检查表结构", check_consumption_record_table()))
    
    # 2. 创建测试数据
    results.append(("创建测试数据", create_test_consumption_records()))
    
    # 3. 测试IP地址显示
    results.append(("IP地址显示", test_ip_address_display()))
    
    # 4. 测试分页功能
    results.append(("分页功能", test_consumption_records_pagination()))
    
    # 输出结果
    print("\n📊 测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 成功" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有功能测试通过！")
        show_usage_instructions()
        return True
    else:
        print("⚠️  部分测试失败，请检查系统配置。")
        return False

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n✅ 消费记录功能测试完成！")
            sys.exit(0)
        else:
            print("\n❌ 消费记录功能测试失败！")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⚠️  测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
