{% extends "base.html" %}

{% block title %}卡密生成成功 - 管理后台{% endblock %}

{% block content %}
<div class="card">
    <h2>卡密生成成功</h2>
    <p style="color: #27ae60; font-weight: bold;">成功生成 {{ cards|length }} 张卡密</p>
    
    <div style="margin: 1rem 0; padding: 1rem; background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px;">
        <strong>重要提醒:</strong> 请立即复制保存以下卡密信息，页面刷新后将无法再次查看！
    </div>
    
    <table class="table">
        <thead>
            <tr>
                <th>卡密</th>
                <th>点数价值</th>
                <th>价格</th>
            </tr>
        </thead>
        <tbody>
            {% for card in cards %}
            <tr>
                <td><code style="font-size: 1.1rem; font-weight: bold;">{{ card.card_code }}</code></td>
                <td>{{ card.points_value }} 点</td>
                <td>¥{{ "%.2f"|format(card.price) }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    
    <div style="text-align: center; margin-top: 2rem;">
        <button onclick="copyAllCards()" class="btn btn-primary">复制所有卡密</button>
        <a href="{{ url_for('admin.create_cards') }}" class="btn btn-success">继续生成</a>
        <a href="{{ url_for('admin.cards') }}" class="btn btn-secondary">查看卡密管理</a>
        <a href="{{ url_for('admin.dashboard') }}" class="btn btn-secondary">返回管理后台</a>
    </div>
</div>

<script>
function copyAllCards() {
    let text = "卡密\t点数价值\t价格\n";
    {% for card in cards %}
    text += "{{ card.card_code }}\t{{ card.points_value }}\t{{ "%.2f"|format(card.price) }}\n";
    {% endfor %}
    
    navigator.clipboard.writeText(text).then(function() {
        alert('卡密信息已复制到剪贴板！');
    }, function(err) {
        console.error('复制失败: ', err);
        // 备用方案
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        alert('卡密信息已复制到剪贴板！');
    });
}
</script>
{% endblock %}
