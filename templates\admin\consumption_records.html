{% extends "base.html" %}

{% block title %}消费记录管理 - 管理后台{% endblock %}

{% block content %}
<div class="card">
    <h2>📊 消费记录管理</h2>
    <p style="color: #666; margin-bottom: 2rem;">查看和管理所有用户的消费记录</p>
    
    {% if records.items %}
    <div style="margin-bottom: 1rem; color: #666;">
        <strong>总记录数:</strong> {{ records.total }} 条
        <span style="margin-left: 2rem;"><strong>当前页:</strong> {{ records.page }} / {{ records.pages }}</span>
    </div>
    
    <table class="table">
        <thead>
            <tr>
                <th>记录ID</th>
                <th>用户名</th>
                <th>软件名称</th>
                <th>消费点数</th>
                <th>消费时间</th>
                <th>IP地址</th>
                <th>用户代理</th>
            </tr>
        </thead>
        <tbody>
            {% for record in records.items %}
            <tr>
                <td>#{{ record.id }}</td>
                <td>
                    <span class="user-badge">
                        {{ record.username or '未知用户' }}
                    </span>
                </td>
                <td>
                    <span class="software-badge">
                        {{ record.software_name or '未知软件' }}
                    </span>
                </td>
                <td>
                    <span class="points-consumed">{{ record.points_consumed }}</span>
                </td>
                <td>{{ record.timestamp }}</td>
                <td>
                    <span class="ip-address">{{ record.ip_address or '未知' }}</span>
                </td>
                <td>
                    <span class="user-agent" title="{{ record.user_agent or '未知' }}">
                        {{ (record.user_agent or '未知')[:30] }}{% if (record.user_agent or '') | length > 30 %}...{% endif %}
                    </span>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    
    <!-- 分页导航 -->
    {% if records.pages > 1 %}
    <div class="pagination-container">
        <div class="pagination">
            {% if records.has_prev %}
                <a href="{{ url_for('admin.consumption_records', page=1) }}" class="page-link">首页</a>
                <a href="{{ url_for('admin.consumption_records', page=records.prev_num) }}" class="page-link">上一页</a>
            {% endif %}
            
            {% for page_num in range(1, records.pages + 1) %}
                {% if page_num == records.page %}
                    <span class="page-link current">{{ page_num }}</span>
                {% elif page_num <= 3 or page_num > records.pages - 3 or (page_num >= records.page - 2 and page_num <= records.page + 2) %}
                    <a href="{{ url_for('admin.consumption_records', page=page_num) }}" class="page-link">{{ page_num }}</a>
                {% elif page_num == 4 or page_num == records.pages - 3 %}
                    <span class="page-link">...</span>
                {% endif %}
            {% endfor %}
            
            {% if records.has_next %}
                <a href="{{ url_for('admin.consumption_records', page=records.next_num) }}" class="page-link">下一页</a>
                <a href="{{ url_for('admin.consumption_records', page=records.pages) }}" class="page-link">末页</a>
            {% endif %}
        </div>
    </div>
    {% endif %}
    
    {% else %}
    <div style="text-align: center; padding: 3rem; color: #666;">
        <h3>📭 暂无消费记录</h3>
        <p>系统中还没有任何消费记录</p>
    </div>
    {% endif %}
</div>

<div class="card">
    <h3>📈 统计信息</h3>
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
        <div class="stat-item">
            <div class="stat-value">{{ records.total or 0 }}</div>
            <div class="stat-label">总消费记录</div>
        </div>
        <div class="stat-item">
            <div class="stat-value">{{ records.per_page }}</div>
            <div class="stat-label">每页显示</div>
        </div>
        <div class="stat-item">
            <div class="stat-value">{{ records.pages or 1 }}</div>
            <div class="stat-label">总页数</div>
        </div>
    </div>
</div>

<style>
.table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
}

.table th,
.table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid #e1e8ed;
}

.table th {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    font-weight: 600;
    color: #2c3e50;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

.user-badge {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.85rem;
    font-weight: 500;
}

.software-badge {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.85rem;
    font-weight: 500;
}

.points-consumed {
    color: #e74c3c;
    font-weight: 600;
}

.ip-address {
    font-family: 'Courier New', monospace;
    background: #f8f9fa;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.85rem;
}

.user-agent {
    font-family: 'Courier New', monospace;
    font-size: 0.8rem;
    color: #666;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.pagination-container {
    margin-top: 2rem;
    display: flex;
    justify-content: center;
}

.pagination {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.page-link {
    padding: 0.5rem 0.75rem;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    color: #667eea;
    text-decoration: none;
    transition: all 0.3s ease;
}

.page-link:hover {
    background: #667eea;
    color: white;
    transform: translateY(-1px);
}

.page-link.current {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-color: #667eea;
}

.stat-item {
    text-align: center;
    padding: 1rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 8px;
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #666;
    font-size: 0.9rem;
}

.card {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.card h2, .card h3 {
    color: #2c3e50;
    margin-bottom: 1rem;
}
</style>
{% endblock %}
