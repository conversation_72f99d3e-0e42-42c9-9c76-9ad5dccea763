#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
修复用户注册时间的时区问题
将UTC时间转换为本地时间（+8小时）
"""

import sys
import os
import sqlite3
from datetime import datetime, timedelta
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def check_user_timestamps():
    """检查用户时间戳格式"""
    print("🔍 检查用户时间戳...")
    
    db_path = "point_card_platform.db"
    
    try:
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # 检查用户表的时间格式
        cursor.execute("""
            SELECT id, username, created_at, last_login 
            FROM user 
            ORDER BY created_at DESC 
            LIMIT 10
        """)
        users = cursor.fetchall()
        
        print("   📋 用户时间戳示例:")
        for user in users:
            print(f"     用户: {user['username']}")
            print(f"       注册时间: {user['created_at']}")
            print(f"       最后登录: {user['last_login']}")
            print()
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"   ❌ 检查用户时间戳失败: {e}")
        return False

def fix_user_created_at():
    """修复用户注册时间（UTC转本地时间）"""
    print("🔧 修复用户注册时间...")
    
    db_path = "point_card_platform.db"
    
    try:
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # 查找需要修复的用户（created_at看起来像UTC时间）
        cursor.execute("""
            SELECT id, username, created_at 
            FROM user 
            WHERE created_at IS NOT NULL
        """)
        users = cursor.fetchall()
        
        fixed_count = 0
        
        for user in users:
            user_id = user['id']
            username = user['username']
            created_at = user['created_at']
            
            try:
                # 尝试解析时间
                if isinstance(created_at, str):
                    # 尝试解析为datetime
                    try:
                        dt = datetime.strptime(created_at, '%Y-%m-%d %H:%M:%S')
                    except ValueError:
                        try:
                            dt = datetime.strptime(created_at, '%Y-%m-%d')
                        except ValueError:
                            print(f"     ⚠️  无法解析用户 {username} 的时间格式: {created_at}")
                            continue
                    
                    # 检查是否需要修复（假设如果时间看起来是UTC，则需要+8小时）
                    # 这里我们假设如果当前时间和注册时间的差异表明注册时间可能是UTC
                    now = datetime.now()
                    time_diff = now - dt
                    
                    # 如果注册时间看起来合理（不是未来时间，且不是太久以前），
                    # 但是小时数看起来像UTC（比本地时间小8小时），则修复
                    local_dt = dt + timedelta(hours=8)
                    
                    # 只有当修正后的时间仍然合理时才进行修复
                    if local_dt <= now:
                        new_time_str = local_dt.strftime('%Y-%m-%d %H:%M:%S')
                        
                        cursor.execute("""
                            UPDATE user SET created_at = ? WHERE id = ?
                        """, (new_time_str, user_id))
                        
                        print(f"     ✅ 修复用户 {username}: {created_at} -> {new_time_str}")
                        fixed_count += 1
                    else:
                        print(f"     ⚠️  跳过用户 {username}: 时间修正后不合理")
                
            except Exception as e:
                print(f"     ❌ 修复用户 {username} 失败: {e}")
        
        conn.commit()
        conn.close()
        
        print(f"   ✅ 修复了 {fixed_count} 个用户的注册时间")
        return True
        
    except Exception as e:
        print(f"   ❌ 修复用户注册时间失败: {e}")
        return False

def fix_user_last_login():
    """修复用户最后登录时间"""
    print("🔧 修复用户最后登录时间...")
    
    db_path = "point_card_platform.db"
    
    try:
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # 查找有最后登录时间的用户
        cursor.execute("""
            SELECT id, username, last_login 
            FROM user 
            WHERE last_login IS NOT NULL AND last_login != ''
        """)
        users = cursor.fetchall()
        
        fixed_count = 0
        
        for user in users:
            user_id = user['id']
            username = user['username']
            last_login = user['last_login']
            
            try:
                # 检查时间格式是否需要修复
                if isinstance(last_login, str) and last_login.strip():
                    # 如果包含T或微秒，需要标准化
                    if 'T' in last_login or '.' in last_login:
                        try:
                            # 解析ISO格式或带微秒的格式
                            if 'T' in last_login:
                                dt = datetime.fromisoformat(last_login.replace('T', ' ').split('.')[0])
                            else:
                                dt = datetime.strptime(last_login.split('.')[0], '%Y-%m-%d %H:%M:%S')
                            
                            # 转换为标准格式
                            new_time_str = dt.strftime('%Y-%m-%d %H:%M:%S')
                            
                            cursor.execute("""
                                UPDATE user SET last_login = ? WHERE id = ?
                            """, (new_time_str, user_id))
                            
                            print(f"     ✅ 修复用户 {username} 最后登录时间: {last_login} -> {new_time_str}")
                            fixed_count += 1
                            
                        except Exception as e:
                            print(f"     ❌ 解析用户 {username} 最后登录时间失败: {e}")
                
            except Exception as e:
                print(f"     ❌ 修复用户 {username} 最后登录时间失败: {e}")
        
        conn.commit()
        conn.close()
        
        print(f"   ✅ 修复了 {fixed_count} 个用户的最后登录时间")
        return True
        
    except Exception as e:
        print(f"   ❌ 修复用户最后登录时间失败: {e}")
        return False

def verify_user_timestamps():
    """验证用户时间戳修复结果"""
    print("🔍 验证用户时间戳修复结果...")
    
    db_path = "point_card_platform.db"
    
    try:
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # 检查修复后的时间格式
        cursor.execute("""
            SELECT id, username, created_at, last_login 
            FROM user 
            ORDER BY created_at DESC 
            LIMIT 5
        """)
        users = cursor.fetchall()
        
        print("   📋 修复后的用户时间戳:")
        for user in users:
            print(f"     用户: {user['username']}")
            print(f"       注册时间: {user['created_at']}")
            print(f"       最后登录: {user['last_login']}")
            
            # 验证时间格式
            created_at = user['created_at']
            if created_at:
                try:
                    dt = datetime.strptime(created_at, '%Y-%m-%d %H:%M:%S')
                    print(f"       ✅ 注册时间格式正确")
                except:
                    print(f"       ❌ 注册时间格式异常")
            
            last_login = user['last_login']
            if last_login:
                try:
                    dt = datetime.strptime(last_login, '%Y-%m-%d %H:%M:%S')
                    print(f"       ✅ 最后登录时间格式正确")
                except:
                    print(f"       ❌ 最后登录时间格式异常")
            
            print()
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"   ❌ 验证用户时间戳失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始修复用户时区问题")
    print("=" * 60)
    
    # 1. 检查当前用户时间戳
    if not check_user_timestamps():
        print("❌ 用户时间戳检查失败")
        return False
    
    # 2. 修复用户注册时间
    if not fix_user_created_at():
        print("❌ 用户注册时间修复失败")
        return False
    
    # 3. 修复用户最后登录时间
    if not fix_user_last_login():
        print("❌ 用户最后登录时间修复失败")
        return False
    
    # 4. 验证修复结果
    if not verify_user_timestamps():
        print("❌ 用户时间戳验证失败")
        return False
    
    print("\n🎉 用户时区问题修复完成！")
    print("现在用户注册时间应该显示正确的本地时间。")
    print("新注册的用户将自动使用本地时间。")
    return True

if __name__ == "__main__":
    if main():
        print("\n✅ 修复成功！用户时间现在应该显示正确的本地时间。")
    else:
        print("\n❌ 修复失败！请检查错误信息。")
        sys.exit(1)
