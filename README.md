# 点卡平台 (Point Card Platform)

[![Python](https://img.shields.io/badge/Python-3.6%2B-blue.svg)](https://python.org)
[![Flask](https://img.shields.io/badge/Flask-2.0%2B-green.svg)](https://flask.palletsprojects.com)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

一个功能完整的点卡管理平台，支持用户注册、点卡充值、消费记录、API集成等功能。采用Flask框架开发，专为稳定性和兼容性设计。

## ✨ 核心特性

### 🔐 用户系统
- **用户注册登录** - 完整的用户认证体系
- **点卡充值** - 支持卡密充值，余额实时查询
- **消费记录** - 详细的消费历史和统计
- **密码管理** - 安全的密码修改功能

### 👨‍💼 管理功能
- **用户管理** - 用户信息查看、编辑、状态管理
- **卡密系统** - 货币金额输入，自动点数转换显示
- **软件管理** - 多软件支持，独立API密钥
- **数据统计** - 消费报表、用户统计、收入分析
- **系统设置** - 灵活的系统参数配置

### 🔑 权限体系
- **普通用户** - 基础功能使用权限
- **管理员** - 用户和卡密管理权限
- **超级管理员** - 完整系统管理，可创建管理员账户

### 🚀 API接口
- **登录认证API** - JWT令牌认证，24小时有效期
- **用户信息API** - 获取用户详细信息
- **点数操作API** - 安全的点数扣除机制
- **状态监控API** - 系统健康检查
- **防拦截机制** - 多重验证防止API滥用

## 🛠️ 技术栈

| 组件 | 技术 | 版本 | 说明 |
|------|------|------|------|
| **后端框架** | Flask | 2.0+ | 轻量级Web框架 |
| **Python版本** | Python | 3.6+ | 兼容CentOS 7.6 |
| **数据库** | SQLite | 3.x | 直接操作，无ORM依赖 |
| **前端UI** | Bootstrap | 5.x | 响应式设计 |
| **样式** | CSS3 | - | 现代渐变设计 |
| **部署** | Gunicorn | - | 生产环境WSGI服务器 |

## 🚀 快速开始

### 📋 环境要求
- Python 3.6+ (推荐 3.9+)
- pip 包管理器
- 4GB+ 可用磁盘空间

### 1️⃣ 环境准备
```bash
# 克隆项目
git clone <repository-url>
cd dianka_platform

# 创建虚拟环境
python3 -m venv venv

# 激活虚拟环境
source venv/bin/activate  # Linux/Mac
# 或 venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt
```

### 2️⃣ 运行应用
```bash
# 开发环境 (调试模式)
python3 run.py

# 生产环境 (推荐)
chmod +x scripts/*.sh
./scripts/start_production.sh
```

### 3️⃣ 访问应用
| 界面 | 地址 | 说明 |
|------|------|------|
| **用户界面** | http://localhost:5000 | 用户注册、登录、充值 |
| **管理后台** | http://localhost:5000/admin | 管理员功能 |
| **API文档** | http://localhost:5000/api/status | API状态检查 |

### 🔑 默认账户
```
超级管理员:
  用户名: admin
  密码: admin123
  权限: 完整系统管理权限
```

## 📁 项目结构

```
dianka_platform/
├── 📄 核心文件
│   ├── app_bypass.py          # 主应用文件（Flask-SQLAlchemy绕过版本）
│   ├── bypass_sqlalchemy.py   # 数据库管理器（直接SQLite操作）
│   ├── run.py                 # 开发环境启动脚本
│   └── requirements.txt       # Python依赖包
│
├── 🛣️ 路由模块 (routes/)
│   ├── admin.py              # 管理员功能路由
│   ├── auth.py               # 用户认证路由
│   ├── user.py               # 用户功能路由
│   └── api.py                # API接口路由
│
├── 🎨 前端资源
│   ├── templates/            # HTML模板文件
│   │   ├── admin/           # 管理员界面模板
│   │   ├── auth/            # 认证界面模板
│   │   ├── user/            # 用户界面模板
│   │   └── base.html        # 基础模板
│   └── static/              # 静态资源文件
│       ├── css/             # 样式文件
│       └── js/              # JavaScript文件
│
├── 🔧 脚本工具 (scripts/)
│   ├── start_production.sh   # 生产环境启动
│   ├── stop_production.sh    # 生产环境停止
│   └── deploy.sh             # 一键部署脚本
│
├── 🧪 测试套件 (tests/)
│   └── test_api_complete.py  # 完整API测试
│
├── 🛠️ 管理工具 (tools/)
│   ├── create_admin.py       # 创建管理员用户
│   └── database_manager.py   # 数据库管理工具
│
├── 📚 文档 (docs/)
│   ├── API_REFERENCE.md      # API接口文档
│   ├── DEPLOYMENT.md         # 部署说明文档
│   └── PROJECT_STATUS.md     # 项目状态文档
│
└── 💾 数据文件
    ├── point_card_platform.db # SQLite数据库文件
    ├── logs/                  # 应用日志目录
    └── instance/              # Flask实例配置
```

## 🚀 API 接口

### 🔐 认证接口
```bash
# 用户登录 - 获取访问令牌
POST /api/login
Content-Type: application/json
{
    "username": "admin",
    "password": "admin123"
}

# 令牌验证 - 验证令牌有效性
POST /api/verify_token
Authorization: Bearer <token>

# 用户登出 - 注销令牌
POST /api/logout
{
    "token": "<access_token>"
}
```

### 👤 用户管理
```bash
# 获取用户信息
POST /api/user_info
{
    "username": "admin"
}

# 查询用户点数
GET /api/user/<username>/points
```

### 💰 点数操作
```bash
# 扣除点数（防拦截机制）
POST /api/deduct
{
    "username": "testuser",
    "points": 10,
    "software_id": 1,
    "api_key": "your_api_key"
}
```

### 📊 系统监控
```bash
# API服务状态
GET /api/status

# 系统健康检查
GET /api/health
```

> 📖 **详细文档**: 查看 [docs/API_REFERENCE.md](docs/API_REFERENCE.md) 获取完整API文档

## 🔧 管理工具

### 创建管理员用户
```bash
# 交互式创建
python3 tools/create_admin.py

# 命令行创建
python3 tools/create_admin.py --username admin2 --password newpass --email <EMAIL>

# 列出所有管理员
python3 tools/create_admin.py --list
```

### 数据库管理
```bash
# 查看数据库信息
python3 tools/database_manager.py --info

# 备份数据库
python3 tools/database_manager.py --backup backup.db

# 恢复数据库
python3 tools/database_manager.py --restore backup.db

# 清理数据库
python3 tools/database_manager.py --clean
```

### 测试工具
```bash
# 完整API测试
python3 tests/test_api_complete.py

# 指定服务器测试
python3 tests/test_api_complete.py --url http://your-server:5000
```

## 🛡️ 安全特性

- **密码哈希**: SHA256 + PBKDF2双重哈希保护
- **令牌认证**: JWT风格令牌，24小时自动过期
- **API防护**: 多重验证防止接口滥用
- **权限控制**: 三级权限体系（用户/管理员/超级管理员）
- **日志记录**: 完整的操作日志和审计跟踪

## 🔍 故障排除

### 常见问题

1. **端口占用错误**
   ```bash
   # 查找占用进程
   lsof -i :5000
   # 或使用停止脚本
   ./scripts/stop_production.sh
   ```

2. **数据库连接失败**
   ```bash
   # 检查数据库文件权限
   ls -la point_card_platform.db
   # 重新初始化数据库
   python3 tools/database_manager.py --reset
   ```

3. **依赖安装失败**
   ```bash
   # 升级pip
   pip install --upgrade pip
   # 重新安装依赖
   pip install -r requirements.txt --force-reinstall
   ```

### 日志查看
```bash
# 查看应用日志
tail -f logs/error.log
tail -f logs/access.log

# 查看系统日志
journalctl -u your-service-name -f
```

## 📋 技术说明

### Flask-SQLAlchemy 绕过方案
本项目采用了 **完全绕过Flask-SQLAlchemy** 的解决方案，直接使用SQLite操作：

- ✅ **兼容性**: 解决CentOS 7.6 + Python 3.6环境问题
- ✅ **稳定性**: 避免ORM版本冲突和依赖问题
- ✅ **性能**: 直接SQL操作，更高效的数据库访问
- ✅ **可控性**: 完全控制SQL语句和事务处理

### 数据库设计
- **用户表**: 支持三级权限体系
- **卡密表**: 货币金额自动转换点数
- **消费记录**: 完整的交易历史
- **API日志**: 接口调用审计跟踪
- **系统设置**: 灵活的配置管理

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 技术支持

- 📧 邮箱: <EMAIL>
- 📱 QQ群: 123456789
- 🌐 官网: https://example.com
- 📖 文档: https://docs.example.com
