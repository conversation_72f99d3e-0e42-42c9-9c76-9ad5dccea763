#!/bin/bash

# 依赖安装修复脚本

echo "🔧 修复依赖安装问题"
echo "=" * 50

# 检测操作系统
if [ -f /etc/redhat-release ]; then
    OS="centos"
    echo "检测到 CentOS/RHEL 系统"
elif [ -f /etc/debian_version ]; then
    OS="ubuntu"
    echo "检测到 Ubuntu/Debian 系统"
else
    echo "⚠️  未知操作系统，请手动安装依赖"
    exit 1
fi

# 安装编译环境
echo "📦 安装编译环境..."
if [ "$OS" = "centos" ]; then
    echo "安装 CentOS/RHEL 编译工具..."
    yum install -y gcc-c++ python3-devel
    yum groupinstall -y "Development Tools"
    
    if [ $? -eq 0 ]; then
        echo "✅ 编译环境安装成功"
    else
        echo "❌ 编译环境安装失败"
        exit 1
    fi
    
elif [ "$OS" = "ubuntu" ]; then
    echo "安装 Ubuntu/Debian 编译工具..."
    apt-get update
    apt-get install -y python3-dev build-essential
    
    if [ $? -eq 0 ]; then
        echo "✅ 编译环境安装成功"
    else
        echo "❌ 编译环境安装失败"
        exit 1
    fi
fi

# 检查虚拟环境
if [ ! -d "venv" ]; then
    echo "❌ 虚拟环境不存在，请先创建虚拟环境"
    echo "运行: python3 -m venv venv"
    exit 1
fi

# 激活虚拟环境
echo "🔄 激活虚拟环境..."
source venv/bin/activate

if [ $? -eq 0 ]; then
    echo "✅ 虚拟环境激活成功"
else
    echo "❌ 虚拟环境激活失败"
    exit 1
fi

# 升级pip
echo "⬆️  升级pip..."
pip install --upgrade pip

if [ $? -eq 0 ]; then
    echo "✅ pip升级成功"
else
    echo "❌ pip升级失败"
    exit 1
fi

# 安装依赖
echo "📦 安装项目依赖..."
pip install -r requirements.txt

if [ $? -eq 0 ]; then
    echo "✅ 依赖安装成功"
else
    echo "❌ 依赖安装失败，尝试逐个安装..."
    
    # 逐个安装依赖
    echo "🔄 逐个安装依赖包..."
    
    # 基础依赖
    pip install Flask==2.0.3
    pip install Werkzeug==2.0.3
    pip install Jinja2==3.0.3
    pip install MarkupSafe==2.0.1
    pip install itsdangerous==2.0.1
    pip install click==8.0.4
    
    # 可选依赖（如果失败则跳过）
    pip install gunicorn || echo "⚠️  gunicorn安装失败，可手动安装"
    pip install requests || echo "⚠️  requests安装失败，可手动安装"
    
    echo "✅ 基础依赖安装完成"
fi

# 验证安装
echo "🧪 验证安装..."
python -c "import flask; print('Flask版本:', flask.__version__)" 2>/dev/null

if [ $? -eq 0 ]; then
    echo "✅ Flask安装验证成功"
else
    echo "❌ Flask安装验证失败"
fi

# 测试应用启动
echo "🚀 测试应用启动..."
timeout 5 python -c "
import sys
sys.path.append('.')
try:
    from app_bypass import app
    print('✅ 应用导入成功')
except Exception as e:
    print(f'❌ 应用导入失败: {e}')
" 2>/dev/null

echo ""
echo "🎉 依赖修复完成！"
echo ""
echo "📋 下一步："
echo "1. 运行: source venv/bin/activate"
echo "2. 启动: python run.py"
echo "3. 或者: ./scripts/start_production.sh"
