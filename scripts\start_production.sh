#!/bin/bash
# 生产环境启动脚本

echo "启动点卡平台生产环境..."

# 检查虚拟环境
if [ ! -d "venv" ]; then
    echo "错误: 虚拟环境不存在，请先运行 python3 -m venv venv"
    exit 1
fi

# 激活虚拟环境
source venv/bin/activate

# 检查依赖
if ! pip show flask > /dev/null 2>&1; then
    echo "安装依赖..."
    pip install -r requirements.txt
fi

# 创建日志目录
mkdir -p logs

# 检查是否已有进程在运行
if [ -f "gunicorn.pid" ]; then
    PID=$(cat gunicorn.pid)
    if ps -p $PID > /dev/null 2>&1; then
        echo "服务已在运行 (PID: $PID)"
        echo "如需重启，请先运行: ./scripts/stop_production.sh"
        exit 1
    else
        rm -f gunicorn.pid
    fi
fi

# 启动Gunicorn
echo "使用Gunicorn启动服务..."

# 检查Gunicorn是否安装
if ! command -v gunicorn &> /dev/null; then
    echo "安装Gunicorn..."
    pip install gunicorn
fi

# 启动服务（移除不兼容的参数）
gunicorn --bind 0.0.0.0:5000 \
         --workers 4 \
         --worker-class sync \
         --timeout 120 \
         --max-requests 1000 \
         --max-requests-jitter 100 \
         --pid gunicorn.pid \
         --daemon \
         --access-logfile logs/access.log \
         --error-logfile logs/error.log \
         --log-level info \
         app_bypass:app

if [ $? -eq 0 ]; then
    echo "服务启动成功！"
    echo "PID文件: $(pwd)/gunicorn.pid"
    echo "访问地址: http://$(hostname -I | awk '{print $1}'):5000"
    echo "日志文件: $(pwd)/logs/"
else
    echo "服务启动失败！"
    exit 1
fi
