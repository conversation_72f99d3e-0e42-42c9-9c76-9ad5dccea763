{% extends "base.html" %}

{% block title %}编辑软件 - 管理后台{% endblock %}

{% block content %}
<div class="card">
    <h2>编辑软件</h2>
    <p style="color: #e74c3c; font-weight: bold;">⚠️ 仅超级管理员可以编辑软件信息</p>
    
    <form method="POST">
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
            <div class="form-group">
                <label for="name">软件名称:</label>
                <input type="text" id="name" name="name" class="form-control" 
                       value="{{ software.name }}" required>
            </div>
            
            <div class="form-group">
                <label for="points_per_use">每次使用扣费点数:</label>
                <input type="number" id="points_per_use" name="points_per_use" 
                       class="form-control" min="1" value="{{ software.points_per_use }}" required>
                <small style="color: #666;">修改后将影响新的API调用</small>
            </div>
        </div>
        
        <div class="form-group">
            <label for="description">软件描述:</label>
            <textarea id="description" name="description" class="form-control" 
                      rows="3" placeholder="可选">{{ software.description or '' }}</textarea>
        </div>
        
        <div style="background: #f8f9fa; padding: 1rem; border-radius: 4px; margin: 1rem 0;">
            <h4 style="color: #2c3e50; margin: 0 0 0.5rem 0;">软件信息</h4>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; font-size: 0.9rem;">
                <div>
                    <strong>软件ID:</strong> {{ software.id }}
                </div>
                <div>
                    <strong>状态:</strong> 
                    {% if software.is_active %}
                        <span style="color: #27ae60;">✓ 启用</span>
                    {% else %}
                        <span style="color: #e74c3c;">✗ 禁用</span>
                    {% endif %}
                </div>
                <div>
                    <strong>API密钥:</strong> 
                    <code style="font-size: 0.8rem;">{{ software.api_key[:20] }}...</code>
                </div>
                <div>
                    <strong>创建时间:</strong> 
                    {% if software.created_at %}
                        {{ software.created_at.strftime('%Y-%m-%d %H:%M') if software.created_at.strftime else software.created_at }}
                    {% else %}
                        未知
                    {% endif %}
                </div>
            </div>
        </div>
        
        <div style="display: flex; gap: 1rem;">
            <button type="submit" class="btn btn-primary">保存修改</button>
            <a href="{{ url_for('admin.software') }}" class="btn btn-secondary">取消</a>
        </div>
    </form>
</div>

<div class="card">
    <h3>⚠️ 重要提醒</h3>
    <ul style="color: #666; line-height: 1.6;">
        <li><strong>扣费点数修改:</strong> 修改后将影响所有新的API调用，已完成的消费记录不会改变</li>
        <li><strong>软件名称:</strong> 修改后可能影响API调用方的识别，请谨慎修改</li>
        <li><strong>API密钥:</strong> 如需重新生成API密钥，请返回软件列表页面操作</li>
        <li><strong>软件状态:</strong> 如需启用/禁用软件，请返回软件列表页面操作</li>
    </ul>
</div>

<style>
.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #2c3e50;
}

.form-control {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e1e8ed;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
    text-align: center;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-2px);
}

.card {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.card h2, .card h3 {
    color: #2c3e50;
    margin-bottom: 1rem;
}

small {
    display: block;
    margin-top: 0.25rem;
}
</style>
{% endblock %}
