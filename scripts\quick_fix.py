#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
快速修复脚本 - 添加缺失的数据库列
"""

import sys
import os
import sqlite3
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def quick_fix():
    """快速修复数据库"""
    print("🔧 快速修复数据库架构...")
    
    db_path = "point_card_platform.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查并添加 description 列
        cursor.execute("PRAGMA table_info(software)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'description' not in columns:
            print("   添加 description 列...")
            cursor.execute("ALTER TABLE software ADD COLUMN description TEXT DEFAULT '暂无描述'")
        
        if 'created_by' not in columns:
            print("   添加 created_by 列...")
            cursor.execute("ALTER TABLE software ADD COLUMN created_by INTEGER DEFAULT 1")
        
        # 检查用户表
        cursor.execute("PRAGMA table_info(user)")
        user_columns = [column[1] for column in cursor.fetchall()]
        
        if 'last_login' not in user_columns:
            print("   添加 last_login 列...")
            cursor.execute("ALTER TABLE user ADD COLUMN last_login TIMESTAMP")
        
        # 更新现有数据
        cursor.execute("UPDATE software SET description = '暂无描述' WHERE description IS NULL")
        cursor.execute("UPDATE software SET created_by = 1 WHERE created_by IS NULL")
        
        conn.commit()
        conn.close()
        
        print("✅ 数据库修复完成！")
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        return False

if __name__ == "__main__":
    if quick_fix():
        print("🎉 修复成功！现在可以正常访问软件列表了。")
    else:
        print("💥 修复失败！请检查错误信息。")
        sys.exit(1)
