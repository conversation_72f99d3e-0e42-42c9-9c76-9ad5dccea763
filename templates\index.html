{% extends "base.html" %}

{% block title %}点卡平台 - 现代化点数管理系统{% endblock %}

{% block content %}
<style>
.hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 4rem 0;
    margin: -2rem -20px 2rem -20px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.hero-content {
    position: relative;
    z-index: 1;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 1rem;
    background: linear-gradient(45deg, #fff, #f8f9fa);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.3rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    font-weight: 300;
}

.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin: 3rem 0;
}

.feature-card {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 10px 40px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border: 1px solid rgba(102, 126, 234, 0.1);
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 60px rgba(0,0,0,0.15);
}

.feature-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    display: block;
}

.feature-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #2c3e50;
}

.feature-description {
    color: #666;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.cta-section {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    padding: 3rem;
    border-radius: 20px;
    text-align: center;
    margin: 3rem 0;
    position: relative;
    overflow: hidden;
}

.cta-section::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px);
    background-size: 20px 20px;
    animation: float 20s infinite linear;
}

@keyframes float {
    0% { transform: translate(0, 0) rotate(0deg); }
    100% { transform: translate(-20px, -20px) rotate(360deg); }
}

.cta-content {
    position: relative;
    z-index: 1;
}

.btn-hero {
    display: inline-block;
    padding: 15px 40px;
    background: white;
    color: #667eea;
    text-decoration: none;
    border-radius: 50px;
    font-weight: 700;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    box-shadow: 0 5px 20px rgba(0,0,0,0.2);
    margin: 0 10px;
}

.btn-hero:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    color: #667eea;
}

.btn-hero.secondary {
    background: transparent;
    color: white;
    border: 2px solid white;
}

.btn-hero.secondary:hover {
    background: white;
    color: #f5576c;
}

.stats-section {
    background: #f8f9fa;
    padding: 3rem;
    border-radius: 20px;
    margin: 3rem 0;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    text-align: center;
}

.stat-item h3 {
    font-size: 2.5rem;
    font-weight: 800;
    color: #667eea;
    margin-bottom: 0.5rem;
}

.stat-item p {
    color: #666;
    font-weight: 500;
}

@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.1rem;
    }
    
    .feature-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<div class="hero-section">
    <div class="hero-content">
        <h1 class="hero-title">🎯 点卡平台</h1>
        <p class="hero-subtitle">现代化的点数管理系统，为开发者和用户提供完整的解决方案</p>
        {% if not current_user.is_authenticated %}
        <div style="margin-top: 2rem;">
            <a href="{{ url_for('auth.login') }}" class="btn-hero">立即登录</a>
            <a href="{{ url_for('auth.register') }}" class="btn-hero secondary">免费注册</a>
        </div>
        {% endif %}
    </div>
</div>

<div class="feature-grid">
    <div class="feature-card">
        <span class="feature-icon">👤</span>
        <h3 class="feature-title">用户管理</h3>
        <p class="feature-description">
            简单快捷的用户注册和登录系统，支持密码修改和账户安全管理。
        </p>
        {% if not current_user.is_authenticated %}
        <a href="{{ url_for('auth.register') }}" class="btn btn-primary">开始使用</a>
        {% endif %}
    </div>
    
    <div class="feature-card">
        <span class="feature-icon">💳</span>
        <h3 class="feature-title">卡密充值</h3>
        <p class="feature-description">
            支持多种面额的卡密充值，安全可靠的点数管理系统。
        </p>
        {% if current_user.is_authenticated and not current_user.is_admin %}
        <a href="{{ url_for('user.recharge') }}" class="btn btn-primary">立即充值</a>
        {% endif %}
    </div>
    
    <div class="feature-card">
        <span class="feature-icon">🔌</span>
        <h3 class="feature-title">API接口</h3>
        <p class="feature-description">
            为开发者提供完整的API接口，轻松集成到任何软件中。
        </p>
        {% if current_user.is_authenticated and current_user.is_admin %}
        <a href="{{ url_for('admin.software') }}" class="btn btn-primary">管理API</a>
        {% endif %}
    </div>
</div>

{% if not current_user.is_authenticated %}
<div class="cta-section">
    <div class="cta-content">
        <h2 style="font-size: 2.5rem; margin-bottom: 1rem; font-weight: 800;">准备开始了吗？</h2>
        <p style="font-size: 1.2rem; margin-bottom: 2rem; opacity: 0.9;">
            加入我们的平台，体验现代化的点数管理系统
        </p>
        <a href="{{ url_for('auth.register') }}" class="btn-hero">免费注册</a>
        <a href="{{ url_for('auth.login') }}" class="btn-hero secondary">已有账户？登录</a>
    </div>
</div>
{% endif %}

<div class="stats-section">
    <h2 style="text-align: center; margin-bottom: 2rem; color: #2c3e50; font-weight: 700;">平台特色</h2>
    <div class="stats-grid">
        <div class="stat-item">
            <h3>🔒</h3>
            <p>安全可靠</p>
        </div>
        <div class="stat-item">
            <h3>⚡</h3>
            <p>快速响应</p>
        </div>
        <div class="stat-item">
            <h3>🎨</h3>
            <p>现代设计</p>
        </div>
        <div class="stat-item">
            <h3>📱</h3>
            <p>移动友好</p>
        </div>
    </div>
</div>
{% endblock %}
