# API 接口文档

## 概述

点卡平台提供完整的RESTful API接口，支持用户认证、点数管理、系统监控等功能。

## 基础信息

- **基础URL**: `http://your-domain:5000/api`
- **认证方式**: Bearer <PERSON>
- **数据格式**: JSON
- **字符编码**: UTF-8

## 通用响应格式

### 成功响应
```json
{
    "success": true,
    "message": "操作成功",
    "data": {
        // 具体数据
    }
}
```

### 错误响应
```json
{
    "success": false,
    "error": "错误描述",
    "error_code": "ERROR_CODE"
}
```

## 接口列表

### 1. 系统状态

#### GET /api/status
获取API服务状态

**请求参数**: 无

**响应示例**:
```json
{
    "success": true,
    "message": "API服务正常",
    "data": {
        "version": "1.0.0",
        "database": "connected",
        "timestamp": "2025-07-03T10:30:00"
    }
}
```

### 2. 用户登录

#### POST /api/login
用户登录认证

**请求参数**:
```json
{
    "username": "admin",
    "password": "admin123"
}
```

**支持的请求方式**:
- JSON格式: `Content-Type: application/json`
- 表单格式: `Content-Type: application/x-www-form-urlencoded`
```

**响应示例**:
```json
{
    "success": true,
    "message": "登录成功",
    "data": {
        "user_id": 1,
        "username": "admin",
        "role": "admin",
        "points": 1000,
        "token": "abc123...",
        "expires_at": "2025-07-04T10:30:00",
        "expires_in": 86400
    }
}
```

**错误码**:
- `MISSING_CREDENTIALS`: 用户名或密码为空
- `INVALID_CREDENTIALS`: 用户名或密码错误
- `LOGIN_FAILED`: 登录失败

### 3. 令牌验证

#### POST /api/verify_token
验证访问令牌

**请求头**:
```
Authorization: Bearer <token>
```

**响应示例**:
```json
{
    "success": true,
    "message": "令牌有效",
    "data": {
        "user_id": 1,
        "username": "admin",
        "expires_at": "2025-07-04T10:30:00"
    }
}
```

### 4. 用户信息

#### GET /api/user_info 或 POST /api/user_info
获取用户详细信息

**请求参数**:
- GET方式: `?username=admin`
- POST方式 (JSON): `{"username": "admin"}`
- POST方式 (表单): `username=admin`

**响应示例**:
```json
{
    "success": true,
    "data": {
        "user_id": 1,
        "username": "admin",
        "role": "admin",
        "points": 1000,
        "created_at": "2025-07-01 10:00:00",
        "last_login": "2025-07-03 10:30:00"
    }
}
```

**错误响应**:
```json
{
    "success": false,
    "error": "用户不存在",
    "error_code": "USER_NOT_FOUND"
}
```

### 5. 用户登出

#### POST /api/logout
用户登出

**请求参数**:
```json
{
    "token": "abc123..."
}
```

**响应示例**:
```json
{
    "success": true,
    "message": "登出成功"
}
```

## 需要API密钥的接口

以下接口需要在请求头中包含API密钥：
```
X-API-Key: your_api_key_here
```

### 6. 检查用户

#### POST /api/check_user
检查用户是否存在并返回点数信息

**请求头**:
```
X-API-Key: your_api_key
```

**请求参数**:
```json
{
    "username": "testuser"
}
```

**响应示例**:
```json
{
    "success": true,
    "data": {
        "user_id": 1,
        "username": "testuser",
        "points": 1000,
        "can_use": true
    }
}
```

### 7. 扣除点数

#### POST /api/deduct_points
扣除用户点数

**请求头**:
```
X-API-Key: your_api_key
```

**请求参数**:
```json
{
    "username": "testuser",
    "points": 10
}
```

**响应示例**:
```json
{
    "success": true,
    "data": {
        "user_id": 1,
        "username": "testuser",
        "points_deducted": 10,
        "remaining_points": 990,
        "record_id": 123
    }
}
```

### 8. 获取余额

#### GET /api/get_balance 或 POST /api/get_balance
获取用户余额

**请求头**:
```
X-API-Key: your_api_key
```

**请求参数**:
- GET方式: `?username=testuser`
- POST方式: `{"username": "testuser"}`

**响应示例**:
```json
{
    "success": true,
    "data": {
        "user_id": 1,
        "username": "testuser",
        "points": 1000
    }
}
```

### 9. 软件信息

#### GET /api/software_info
获取软件信息

**请求头**:
```
X-API-Key: your_api_key
```

**响应示例**:
```json
{
    "success": true,
    "data": {
        "software_id": 1,
        "name": "测试软件",
        "description": "软件描述",
        "points_per_use": 1,
        "is_active": true
    }
}
```

### 10. 使用历史

#### GET /api/usage_history 或 POST /api/usage_history
获取软件使用历史

**请求头**:
```
X-API-Key: your_api_key
```

**请求参数**:
- `username` (可选): 指定用户名
- `limit` (可选): 记录数量限制，默认50，最大100

**响应示例**:
```json
{
    "success": true,
    "data": [
        {
            "record_id": 123,
            "username": "testuser",
            "points_consumed": 10,
            "timestamp": "2025-07-03 10:30:00",
            "ip_address": "*************"
        }
    ],
    "total_records": 1
}
```

### 11. 获取验证挑战

#### GET /api/get_challenge
获取验证挑战（用于严格验证模式）

**请求头**:
```
X-API-Key: your_api_key
```

**响应示例**:
```json
{
    "success": true,
    "data": {
        "challenge": "a1b2c3d4e5f6...",
        "timestamp": "1672531200.123",
        "expires_in": 300
    }
}
```

## 错误码说明

### 认证相关错误
| 错误码 | 说明 |
|--------|------|
| `MISSING_CREDENTIALS` | 缺少用户名或密码 |
| `INVALID_CREDENTIALS` | 用户名或密码错误 |
| `LOGIN_FAILED` | 登录失败，请重试 |
| `MISSING_TOKEN` | 访问令牌缺失 |
| `TOKEN_VERIFICATION_FAILED` | 令牌验证失败 |
| `LOGOUT_FAILED` | 登出失败 |

### 用户相关错误
| 错误码 | 说明 |
|--------|------|
| `MISSING_USERNAME` | 用户名缺失 |
| `USER_NOT_FOUND` | 用户不存在 |
| `GET_USER_INFO_FAILED` | 获取用户信息失败 |

### API密钥相关错误
| 错误码 | 说明 |
|--------|------|
| `MISSING_API_KEY` | API密钥缺失 |
| `INVALID_API_KEY` | 无效的API密钥 |
| `SOFTWARE_DISABLED` | 软件已被禁用 |
| `RATE_LIMIT_EXCEEDED` | API调用频率超限 |

### 安全验证错误
| 错误码 | 说明 |
|--------|------|
| `MISSING_SECURITY_PARAMS` | 缺少安全验证参数 |
| `SECURITY_VERIFICATION_FAILED` | 安全验证失败 |

### 点数操作错误
| 错误码 | 说明 |
|--------|------|
| `INSUFFICIENT_POINTS` | 点数不足 |
| `DEDUCTION_FAILED` | 扣费失败，请重试 |

### 其他错误
| 错误码 | 说明 |
|--------|------|
| `GET_HISTORY_FAILED` | 获取使用历史失败 |

## 使用示例

### Python 示例

#### 登录认证API示例
```python
import requests

BASE_URL = "http://localhost:5000/api"

# 1. 用户登录
def login(username, password):
    url = f"{BASE_URL}/login"
    data = {"username": username, "password": password}
    response = requests.post(url, json=data)
    return response.json()

# 2. 获取用户信息
def get_user_info(username):
    url = f"{BASE_URL}/user_info"
    data = {"username": username}
    response = requests.post(url, json=data)
    return response.json()

# 3. 验证令牌
def verify_token(token):
    url = f"{BASE_URL}/verify_token"
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.post(url, headers=headers)
    return response.json()

# 使用示例
if __name__ == "__main__":
    # 登录
    result = login("admin", "admin123")
    if result["success"]:
        token = result["data"]["token"]
        print(f"登录成功，令牌: {token}")

        # 获取用户信息
        user_info = get_user_info("admin")
        print(f"用户信息: {user_info}")

        # 验证令牌
        verify_result = verify_token(token)
        print(f"令牌验证: {verify_result}")
    else:
        print(f"登录失败: {result['error']}")
```

#### API密钥接口示例
```python
import requests

BASE_URL = "http://localhost:5000/api"
API_KEY = "your_api_key_here"

# 1. 检查用户
def check_user(username):
    url = f"{BASE_URL}/check_user"
    headers = {"X-API-Key": API_KEY}
    data = {"username": username}
    response = requests.post(url, json=data, headers=headers)
    return response.json()

# 2. 扣除点数
def deduct_points(username, points):
    url = f"{BASE_URL}/deduct_points"
    headers = {"X-API-Key": API_KEY}
    data = {"username": username, "points": points}
    response = requests.post(url, json=data, headers=headers)
    return response.json()

# 3. 获取余额
def get_balance(username):
    url = f"{BASE_URL}/get_balance"
    headers = {"X-API-Key": API_KEY}
    params = {"username": username}
    response = requests.get(url, params=params, headers=headers)
    return response.json()

# 使用示例
if __name__ == "__main__":
    username = "testuser"

    # 检查用户
    user_check = check_user(username)
    print(f"用户检查: {user_check}")

    if user_check["success"] and user_check["data"]["can_use"]:
        # 扣除点数
        deduct_result = deduct_points(username, 10)
        print(f"扣除结果: {deduct_result}")

        # 查询余额
        balance = get_balance(username)
        print(f"当前余额: {balance}")
```

### cURL 示例

#### 登录认证API
```bash
# 1. 用户登录
curl -X POST http://localhost:5000/api/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}'

# 2. 获取用户信息
curl -X POST http://localhost:5000/api/user_info \
  -H "Content-Type: application/json" \
  -d '{"username":"admin"}'

# 3. 验证令牌
curl -X POST http://localhost:5000/api/verify_token \
  -H "Authorization: Bearer <your_token_here>"

# 4. 用户登出
curl -X POST http://localhost:5000/api/logout \
  -H "Content-Type: application/json" \
  -d '{"token":"<your_token_here>"}'
```

#### API密钥接口
```bash
# 1. 检查用户
curl -X POST http://localhost:5000/api/check_user \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your_api_key" \
  -d '{"username":"testuser"}'

# 2. 扣除点数
curl -X POST http://localhost:5000/api/deduct_points \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your_api_key" \
  -d '{"username":"testuser","points":10}'

# 3. 获取余额
curl -X GET "http://localhost:5000/api/get_balance?username=testuser" \
  -H "X-API-Key: your_api_key"

# 4. 获取软件信息
curl -X GET http://localhost:5000/api/software_info \
  -H "X-API-Key: your_api_key"

# 5. 获取使用历史
curl -X GET "http://localhost:5000/api/usage_history?username=testuser&limit=10" \
  -H "X-API-Key: your_api_key"
```

## 安全说明

1. **HTTPS**: 生产环境建议使用HTTPS
2. **令牌管理**: 令牌有效期24小时，请妥善保管
3. **API密钥**: 点数扣除接口需要有效的API密钥
4. **频率限制**: 部分接口有请求频率限制
5. **IP白名单**: 可配置API访问IP白名单
