from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_user, logout_user, login_required, current_user
from datetime import datetime
import re

# 导入绕过方案
from bypass_sqlalchemy import db_manager, SimpleUser

auth_bp = Blueprint('auth', __name__)

def validate_email(email):
    """验证邮箱格式"""
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def validate_username(username):
    """验证用户名格式（只允许英文字母和数字）"""
    if len(username) < 3:
        return False, "用户名长度至少3位"
    if len(username) > 20:
        return False, "用户名长度不能超过20位"

    # 只允许英文字母和数字
    pattern = r'^[a-zA-Z0-9]+$'
    if not re.match(pattern, username):
        return False, "用户名只能包含英文字母和数字"

    # 必须以字母开头
    if not username[0].isalpha():
        return False, "用户名必须以英文字母开头"

    return True, ""

def validate_password(password):
    """验证密码强度"""
    if len(password) < 6:
        return False, "密码长度至少6位"
    return True, ""

@auth_bp.route('/register', methods=['GET', 'POST'])
def register():
    """用户注册"""
    if request.method == 'POST':
        username = request.form.get('username', '').strip()
        email = request.form.get('email', '').strip()
        password = request.form.get('password', '')
        confirm_password = request.form.get('confirm_password', '')
        
        # 验证输入
        if not username or not email or not password:
            flash('所有字段都是必填的', 'error')
            return render_template('auth/register.html')

        # 验证用户名格式
        is_valid_username, username_msg = validate_username(username)
        if not is_valid_username:
            flash(username_msg, 'error')
            return render_template('auth/register.html')

        if not validate_email(email):
            flash('邮箱格式不正确', 'error')
            return render_template('auth/register.html')
        
        is_valid, msg = validate_password(password)
        if not is_valid:
            flash(msg, 'error')
            return render_template('auth/register.html')
        
        if password != confirm_password:
            flash('两次输入的密码不一致', 'error')
            return render_template('auth/register.html')
        
        # 检查用户名是否已存在
        existing_user = db_manager.get_user_by_username(username)
        if existing_user:
            flash('用户名已存在', 'error')
            return render_template('auth/register.html')

        # 检查邮箱是否已存在
        with db_manager.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT id FROM user WHERE email = ?", (email,))
            if cursor.fetchone():
                flash('邮箱已被注册', 'error')
                return render_template('auth/register.html')
        
        # 创建新用户
        password_hash = db_manager.hash_password(password)

        try:
            with db_manager.get_connection() as conn:
                cursor = conn.cursor()
                # 使用本地时间而不是数据库默认的UTC时间
                current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                cursor.execute("""
                    INSERT INTO user (username, email, password_hash, is_admin, is_super_admin, points, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (username, email, password_hash, False, False, 0, current_time))
                conn.commit()

            flash('注册成功！请登录', 'success')
            return redirect(url_for('auth.login'))
        except Exception as e:
            flash(f'注册失败: {str(e)}', 'error')
            return render_template('auth/register.html')
    
    return render_template('auth/register.html')

@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    """用户登录"""
    if request.method == 'POST':
        username = request.form.get('username', '').strip()
        password = request.form.get('password', '')
        remember = bool(request.form.get('remember'))
        
        if not username or not password:
            flash('请输入用户名和密码', 'error')
            return render_template('auth/login.html')
        
        # 使用绕过方案验证用户
        user_data = db_manager.authenticate_user(username, password)
        if user_data:
            user = SimpleUser(user_data)

            # 更新最后登录时间
            try:
                db_manager.update_user_last_login(user.id)
            except Exception as e:
                print(f"更新最后登录时间失败: {e}")

            login_user(user, remember=remember)

            # 根据用户类型重定向
            if user.is_admin:
                return redirect(url_for('admin.dashboard'))
            else:
                return redirect(url_for('user.dashboard'))
        else:
            flash('用户名或密码错误', 'error')
    
    return render_template('auth/login.html')

@auth_bp.route('/logout')
@login_required
def logout():
    """用户登出"""
    logout_user()
    flash('已成功登出', 'info')
    return redirect(url_for('index'))

@auth_bp.route('/change_password', methods=['GET', 'POST'])
@login_required
def change_password():
    """修改密码"""
    if request.method == 'POST':
        current_password = request.form.get('current_password', '')
        new_password = request.form.get('new_password', '')
        confirm_password = request.form.get('confirm_password', '')
        
        if not current_password or not new_password or not confirm_password:
            flash('所有字段都是必填的', 'error')
            return render_template('auth/change_password.html')
        
        if not current_user.check_password(current_password):
            flash('当前密码错误', 'error')
            return render_template('auth/change_password.html')
        
        is_valid, msg = validate_password(new_password)
        if not is_valid:
            flash(msg, 'error')
            return render_template('auth/change_password.html')
        
        if new_password != confirm_password:
            flash('两次输入的新密码不一致', 'error')
            return render_template('auth/change_password.html')
        
        # 更新密码
        new_password_hash = db_manager.hash_password(new_password)

        with db_manager.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE user SET password_hash = ? WHERE id = ?
            """, (new_password_hash, current_user.id))
            conn.commit()

        # 密码修改成功后自动退出账号
        logout_user()
        flash('密码修改成功，请重新登录', 'success')
        return redirect(url_for('auth.login'))
    
    return render_template('auth/change_password.html')
