{% extends "base.html" %}

{% block title %}创建卡密 - 管理后台{% endblock %}

{% block content %}
<div class="card" style="max-width: 500px; margin: 2rem auto;">
    <h2>创建卡密</h2>
    
    <form method="POST">
        {% if card_value_mode == 'fixed' and not current_user.is_super_admin %}
        <div class="form-group">
            <label for="amount">选择卡密金额:</label>
            <select id="amount" name="amount" class="form-control" required onchange="updatePoints()">
                <option value="">请选择金额</option>
                {% for amount in fixed_card_amounts %}
                <option value="{{ amount }}">{{ amount }} 元</option>
                {% endfor %}
            </select>
            <small style="color: #666;">只能创建系统预设的固定金额</small>
        </div>
        {% else %}
        <div class="form-group">
            <label for="amount">卡密金额 (元):</label>
            <input type="number" id="amount" name="amount" class="form-control"
                   min="1" step="1" placeholder="例如: 10" required onchange="updatePoints()">
            <small style="color: #666;">输入卡密的实际金额</small>
            {% if current_user.is_super_admin %}
            <small style="color: #27ae60; display: block;">超级管理员可以设置任意金额</small>
            {% endif %}
        </div>
        {% endif %}

        <div class="form-group">
            <label>可兑换点数:</label>
            <div id="points-display" style="font-size: 1.2rem; font-weight: bold; color: #667eea; padding: 10px; background: #f8f9fa; border-radius: 4px;">
                请先输入金额
            </div>
            <small style="color: #666;">当前汇率: 1元 = {{ yuan_to_point_rate }} 点</small>
        </div>

        <div class="form-group">
            <label for="quantity">生成数量:</label>
            <input type="number" id="quantity" name="quantity" class="form-control"
                   min="1" max="100" value="1" required>
            <small style="color: #666;">一次最多生成100张卡密</small>
        </div>

        <button type="submit" class="btn btn-primary" style="width: 100%;">生成卡密</button>
    </form>

    <script>
    function updatePoints() {
        const amountInput = document.getElementById('amount');
        const pointsDisplay = document.getElementById('points-display');
        const rate = {{ yuan_to_point_rate }};

        const amount = parseInt(amountInput.value) || 0;
        if (amount > 0) {
            const points = amount * rate;
            pointsDisplay.textContent = `${amount} 元 = ${points} 点`;
            pointsDisplay.style.color = '#667eea';
        } else {
            pointsDisplay.textContent = '请先输入金额';
            pointsDisplay.style.color = '#999';
        }
    }

    // 页面加载时更新一次
    document.addEventListener('DOMContentLoaded', function() {
        updatePoints();
    });
    </script>
    
    <div style="margin-top: 2rem; padding: 1rem; background-color: #f8f9fa; border-radius: 4px;">
        <h4 style="color: #555; margin-bottom: 1rem;">说明:</h4>
        <ul style="color: #666; line-height: 1.8;">
            <li>卡密将自动生成，无需卡号</li>
            <li>卡密为20位字母和数字组合</li>
            <li>输入实际金额，系统自动计算对应点数</li>
            <li>生成后请妥善保管卡密信息</li>
            {% if card_value_mode == 'fixed' %}
            <li style="color: #e74c3c;">当前为固定金额模式</li>
            {% endif %}
        </ul>
    </div>
    
    <div style="text-align: center; margin-top: 1rem;">
        <a href="{{ url_for('admin.dashboard') }}" class="btn btn-secondary">返回管理后台</a>
    </div>
</div>
{% endblock %}
