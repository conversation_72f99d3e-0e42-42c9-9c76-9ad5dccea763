#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
数据库架构修复脚本
添加缺失的列和修复数据库结构问题
"""

import sys
import os
import sqlite3
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from bypass_sqlalchemy import db_manager

def check_column_exists(cursor, table_name, column_name):
    """检查列是否存在"""
    cursor.execute(f"PRAGMA table_info({table_name})")
    columns = [column[1] for column in cursor.fetchall()]
    return column_name in columns

def fix_software_table():
    """修复软件表结构"""
    print("🔧 修复软件表结构...")
    
    try:
        with db_manager.get_connection() as conn:
            cursor = conn.cursor()
            
            # 检查是否存在 description 列
            if not check_column_exists(cursor, 'software', 'description'):
                print("   添加 description 列...")
                cursor.execute("ALTER TABLE software ADD COLUMN description TEXT")
                
            # 检查是否存在 created_by 列
            if not check_column_exists(cursor, 'software', 'created_by'):
                print("   添加 created_by 列...")
                cursor.execute("ALTER TABLE software ADD COLUMN created_by INTEGER")
                
            # 检查是否存在 last_login 列
            if not check_column_exists(cursor, 'user', 'last_login'):
                print("   添加 last_login 列...")
                cursor.execute("ALTER TABLE user ADD COLUMN last_login TIMESTAMP")
            
            conn.commit()
            print("   ✅ 软件表结构修复完成")
            return True
            
    except Exception as e:
        print(f"   ❌ 修复软件表失败: {e}")
        return False

def update_existing_software():
    """更新现有软件数据"""
    print("📝 更新现有软件数据...")
    
    try:
        with db_manager.get_connection() as conn:
            cursor = conn.cursor()
            
            # 为没有描述的软件添加默认描述
            cursor.execute("""
                UPDATE software 
                SET description = '暂无描述' 
                WHERE description IS NULL OR description = ''
            """)
            
            # 为没有创建者的软件设置默认创建者
            cursor.execute("""
                UPDATE software 
                SET created_by = 1 
                WHERE created_by IS NULL
            """)
            
            conn.commit()
            print("   ✅ 软件数据更新完成")
            return True
            
    except Exception as e:
        print(f"   ❌ 更新软件数据失败: {e}")
        return False

def verify_database_structure():
    """验证数据库结构"""
    print("🔍 验证数据库结构...")
    
    try:
        with db_manager.get_connection() as conn:
            cursor = conn.cursor()
            
            # 检查软件表结构
            cursor.execute("PRAGMA table_info(software)")
            software_columns = [column[1] for column in cursor.fetchall()]
            
            required_columns = ['id', 'name', 'description', 'api_key', 'points_per_use', 'is_active', 'created_at', 'created_by']
            missing_columns = [col for col in required_columns if col not in software_columns]
            
            if missing_columns:
                print(f"   ❌ 软件表缺少列: {missing_columns}")
                return False
            else:
                print("   ✅ 软件表结构正确")
            
            # 检查用户表结构
            cursor.execute("PRAGMA table_info(user)")
            user_columns = [column[1] for column in cursor.fetchall()]
            
            user_required_columns = ['id', 'username', 'email', 'password_hash', 'points', 'is_admin', 'is_super_admin', 'created_at', 'last_login']
            user_missing_columns = [col for col in user_required_columns if col not in user_columns]
            
            if user_missing_columns:
                print(f"   ❌ 用户表缺少列: {user_missing_columns}")
                return False
            else:
                print("   ✅ 用户表结构正确")
            
            # 测试查询软件列表
            cursor.execute("SELECT id, name, description, points_per_use, is_active FROM software LIMIT 1")
            result = cursor.fetchone()
            print("   ✅ 软件查询测试通过")
            
            return True
            
    except Exception as e:
        print(f"   ❌ 验证数据库结构失败: {e}")
        return False

def create_test_software():
    """创建测试软件"""
    print("🧪 创建测试软件...")
    
    try:
        with db_manager.get_connection() as conn:
            cursor = conn.cursor()
            
            # 检查是否已有测试软件
            cursor.execute("SELECT COUNT(*) FROM software WHERE name = ?", ('测试软件V2',))
            if cursor.fetchone()[0] == 0:
                cursor.execute("""
                    INSERT INTO software (name, description, api_key, points_per_use, is_active, created_by)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, ('测试软件V2', '这是一个用于测试的软件应用', 'test_api_key_v2_123456', 5, 1, 1))
                
                cursor.execute("""
                    INSERT INTO software (name, description, api_key, points_per_use, is_active, created_by)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, ('图像处理工具', '专业的图像处理和编辑软件', 'image_tool_api_key_789', 10, 1, 1))
                
                conn.commit()
                print("   ✅ 测试软件创建完成")
            else:
                print("   ℹ️  测试软件已存在")
            
            return True
            
    except Exception as e:
        print(f"   ❌ 创建测试软件失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始数据库架构修复")
    print("=" * 60)
    
    # 1. 修复软件表结构
    if not fix_software_table():
        print("❌ 数据库架构修复失败")
        return False
    
    # 2. 更新现有数据
    if not update_existing_software():
        print("❌ 数据更新失败")
        return False
    
    # 3. 验证数据库结构
    if not verify_database_structure():
        print("❌ 数据库结构验证失败")
        return False
    
    # 4. 创建测试软件
    if not create_test_software():
        print("❌ 测试软件创建失败")
        return False
    
    print("\n🎉 数据库架构修复完成！")
    print("现在可以正常访问软件列表页面了。")
    return True

if __name__ == "__main__":
    if main():
        print("\n✅ 修复成功！请重启应用程序。")
    else:
        print("\n❌ 修复失败！请检查错误信息。")
        sys.exit(1)
