#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
修复 consumption_record 表结构问题
确保表中有 ip_address 和 user_agent 字段
"""

import sys
import os
import sqlite3
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def check_table_structure():
    """检查 consumption_record 表结构"""
    print("🔍 检查 consumption_record 表结构...")
    
    try:
        conn = sqlite3.connect('point_card_platform.db')
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='consumption_record'
        """)
        
        if not cursor.fetchone():
            print("   ❌ consumption_record 表不存在")
            conn.close()
            return False, []
        
        # 检查表结构
        cursor.execute("PRAGMA table_info(consumption_record)")
        columns = cursor.fetchall()
        
        print("   📋 当前表结构:")
        column_names = []
        for col in columns:
            column_names.append(col[1])
            print(f"      - {col[1]}: {col[2]} (NOT NULL: {col[3]})")
        
        conn.close()
        return True, column_names
        
    except Exception as e:
        print(f"   ❌ 检查表结构失败: {e}")
        return False, []

def add_missing_columns(missing_columns):
    """添加缺失的字段"""
    print("🔧 添加缺失的字段...")
    
    try:
        conn = sqlite3.connect('point_card_platform.db')
        cursor = conn.cursor()
        
        for column in missing_columns:
            if column == 'ip_address':
                print("   📝 添加 ip_address 字段...")
                cursor.execute("ALTER TABLE consumption_record ADD COLUMN ip_address VARCHAR(45)")
                print("   ✅ ip_address 字段添加成功")
            elif column == 'user_agent':
                print("   📝 添加 user_agent 字段...")
                cursor.execute("ALTER TABLE consumption_record ADD COLUMN user_agent TEXT")
                print("   ✅ user_agent 字段添加成功")
        
        conn.commit()
        conn.close()
        return True
        
    except Exception as e:
        print(f"   ❌ 添加字段失败: {e}")
        return False

def test_recent_records_query():
    """测试最近消费记录查询"""
    print("🧪 测试最近消费记录查询...")
    
    try:
        from bypass_sqlalchemy import DatabaseManager
        
        db_manager = DatabaseManager()
        
        # 测试获取最近消费记录
        recent_records = db_manager.get_recent_consumption_records(5)
        
        print(f"   📋 获取到 {len(recent_records)} 条最近消费记录")
        
        if recent_records:
            print("   📋 第一条记录字段:")
            record = recent_records[0]
            for key, value in record.items():
                if key not in ['user', 'software']:  # 跳过嵌套对象
                    print(f"      {key}: {value}")
        
        print("   ✅ 最近消费记录查询测试通过")
        return True
        
    except Exception as e:
        print(f"   ❌ 测试最近消费记录查询失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_user_email_method():
    """测试 get_user_by_email 方法"""
    print("📧 测试 get_user_by_email 方法...")
    
    try:
        from bypass_sqlalchemy import DatabaseManager
        
        db_manager = DatabaseManager()
        
        # 获取一个已存在的用户邮箱进行测试
        with db_manager.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT email FROM user LIMIT 1")
            result = cursor.fetchone()
            
            if not result:
                print("   ⚠️  没有用户数据，跳过测试")
                return True
            
            test_email = result[0]
        
        # 测试 get_user_by_email 方法
        user = db_manager.get_user_by_email(test_email)
        
        if user:
            print(f"   ✅ 通过邮箱获取用户成功: {user['username']} ({user['email']})")
            return True
        else:
            print(f"   ❌ 通过邮箱获取用户失败")
            return False
        
    except Exception as e:
        print(f"   ❌ 测试 get_user_by_email 方法失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_test_consumption_record():
    """创建测试消费记录"""
    print("📝 创建测试消费记录...")
    
    try:
        conn = sqlite3.connect('point_card_platform.db')
        cursor = conn.cursor()
        
        # 检查是否已有消费记录
        cursor.execute("SELECT COUNT(*) FROM consumption_record")
        count = cursor.fetchone()[0]
        
        if count > 0:
            print(f"   ℹ️  已有 {count} 条消费记录，跳过创建")
            conn.close()
            return True
        
        # 获取用户和软件ID
        cursor.execute("SELECT id FROM user LIMIT 1")
        user_result = cursor.fetchone()
        if not user_result:
            print("   ❌ 未找到用户")
            conn.close()
            return False
        user_id = user_result[0]
        
        cursor.execute("SELECT id FROM software LIMIT 1")
        software_result = cursor.fetchone()
        if not software_result:
            print("   ❌ 未找到软件")
            conn.close()
            return False
        software_id = software_result[0]
        
        # 创建测试消费记录
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        cursor.execute("""
            INSERT INTO consumption_record (user_id, software_id, points_consumed, ip_address, user_agent, timestamp)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (user_id, software_id, 10, '127.0.0.1', 'Test User Agent', timestamp))
        
        conn.commit()
        conn.close()
        
        print("   ✅ 创建测试消费记录成功")
        return True
        
    except Exception as e:
        print(f"   ❌ 创建测试消费记录失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始修复 consumption_record 表结构")
    print("=" * 60)
    
    # 1. 检查表结构
    table_exists, current_columns = check_table_structure()
    if not table_exists:
        print("❌ consumption_record 表不存在，需要先初始化数据库")
        return False
    
    # 2. 检查必要字段
    required_columns = ['id', 'user_id', 'software_id', 'points_consumed', 'timestamp', 'ip_address', 'user_agent']
    missing_columns = [col for col in required_columns if col not in current_columns]
    
    if missing_columns:
        print(f"   ⚠️  缺少字段: {missing_columns}")
        if not add_missing_columns(missing_columns):
            print("❌ 添加缺失字段失败")
            return False
    else:
        print("   ✅ 表结构完整")
    
    # 3. 测试 get_user_by_email 方法
    if not test_user_email_method():
        print("❌ get_user_by_email 方法测试失败")
        return False
    
    # 4. 创建测试数据
    if not create_test_consumption_record():
        print("❌ 创建测试数据失败")
        return False
    
    # 5. 测试最近消费记录查询
    if not test_recent_records_query():
        print("❌ 最近消费记录查询测试失败")
        return False
    
    print("\n🎉 consumption_record 表结构修复完成！")
    print("✅ 现在管理后台仪表板应该可以正常工作")
    print("✅ 用户编辑功能也应该可以正常工作")
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n✅ 修复成功！")
            print("现在可以正常使用以下功能：")
            print("- 管理后台仪表板")
            print("- 超级管理员编辑用户")
            print("- 消费记录显示")
            sys.exit(0)
        else:
            print("\n❌ 修复失败！")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⚠️  修复被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 修复过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
