{% extends "base.html" %}

{% block title %}卡密管理 - 管理后台{% endblock %}

{% block content %}
<style>
.admin-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    border-radius: 15px;
    margin-bottom: 2rem;
    text-align: center;
}

.filter-section {
    background: white;
    padding: 1.5rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.filter-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 8px 20px;
    border: 2px solid #667eea;
    background: white;
    color: #667eea;
    border-radius: 25px;
    text-decoration: none;
    transition: all 0.3s ease;
    font-weight: 500;
}

.filter-btn:hover, .filter-btn.active {
    background: #667eea;
    color: white;
    transform: translateY(-2px);
}

.card-table {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.table-header {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    padding: 1rem;
}

.status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.status-used {
    background: #e74c3c;
    color: white;
}

.status-unused {
    background: #27ae60;
    color: white;
}

.card-code {
    font-family: 'Courier New', monospace;
    background: #f8f9fa;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.9rem;
}

.pagination-modern {
    display: flex;
    justify-content: center;
    margin-top: 2rem;
    gap: 0.5rem;
}

.pagination-modern a,
.pagination-modern span {
    padding: 10px 15px;
    border-radius: 10px;
    text-decoration: none;
    transition: all 0.3s ease;
    font-weight: 500;
}

.pagination-modern a {
    background: white;
    color: #667eea;
    border: 2px solid #667eea;
}

.pagination-modern a:hover {
    background: #667eea;
    color: white;
    transform: translateY(-2px);
}

.pagination-modern .current {
    background: #667eea;
    color: white;
    border: 2px solid #667eea;
}
</style>

<div class="admin-header">
    <h1>💳 卡密管理</h1>
    <p>管理系统中的所有卡密，查看使用状态和详细信息</p>
</div>

<div class="filter-section">
    <h3 style="margin-bottom: 1rem; color: #2c3e50;">筛选卡密</h3>
    <div class="filter-buttons">
        <a href="{{ url_for('admin.cards') }}" class="filter-btn {{ 'active' if status == 'all' else '' }}">
            全部卡密
        </a>
        <a href="{{ url_for('admin.cards', status='unused') }}" class="filter-btn {{ 'active' if status == 'unused' else '' }}">
            未使用
        </a>
        <a href="{{ url_for('admin.cards', status='used') }}" class="filter-btn {{ 'active' if status == 'used' else '' }}">
            已使用
        </a>
    </div>
</div>

{% if cards.items %}
<div class="card-table">
    <div class="table-header">
        <h3 style="margin: 0;">卡密列表</h3>
    </div>
    
    <table class="table" style="margin: 0;">
        <thead style="background: #f8f9fa;">
            <tr>
                <th style="padding: 1rem;">卡密</th>
                <th style="padding: 1rem;">点数价值</th>
                <th style="padding: 1rem;">价格</th>
                <th style="padding: 1rem;">状态</th>
                <th style="padding: 1rem;">使用者</th>
                <th style="padding: 1rem;">创建时间</th>
                <th style="padding: 1rem;">使用时间</th>
            </tr>
        </thead>
        <tbody>
            {% for card in cards.items %}
            <tr style="border-bottom: 1px solid #eee;">
                <td style="padding: 1rem;">
                    <span class="card-code">{{ card.card_code }}</span>
                </td>
                <td style="padding: 1rem;">
                    <strong style="color: #667eea;">{{ card.points_value }}</strong> 点
                </td>
                <td style="padding: 1rem;">
                    <strong style="color: #27ae60;">¥{{ "%.2f"|format(card.price) }}</strong>
                </td>
                <td style="padding: 1rem;">
                    {% if card.is_used %}
                        <span class="status-badge status-used">已使用</span>
                    {% else %}
                        <span class="status-badge status-unused">未使用</span>
                    {% endif %}
                </td>
                <td style="padding: 1rem;">
                    {% if card.is_used and card.used_by %}
                        {{ card.user.username if card.user else '未知用户' }}
                    {% else %}
                        <span style="color: #999;">-</span>
                    {% endif %}
                </td>
                <td style="padding: 1rem;">
                    {% if card.created_at %}
                        <small style="color: #666;">{{ card.created_at.strftime('%Y-%m-%d %H:%M') }}</small>
                    {% else %}
                        <span style="color: #999;">-</span>
                    {% endif %}
                </td>
                <td style="padding: 1rem;">
                    {% if card.used_at %}
                        <small style="color: #666;">{{ card.used_at.strftime('%Y-%m-%d %H:%M') }}</small>
                    {% else %}
                        <span style="color: #999;">-</span>
                    {% endif %}
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<!-- 分页 -->
{% if cards.pages > 1 %}
<div class="pagination-modern">
    {% if cards.has_prev %}
        <a href="{{ url_for('admin.cards', page=cards.prev_num, status=status) }}">‹ 上一页</a>
    {% endif %}
    
    {% for page_num in cards.iter_pages() %}
        {% if page_num %}
            {% if page_num != cards.page %}
                <a href="{{ url_for('admin.cards', page=page_num, status=status) }}">{{ page_num }}</a>
            {% else %}
                <span class="current">{{ page_num }}</span>
            {% endif %}
        {% else %}
            <span>...</span>
        {% endif %}
    {% endfor %}
    
    {% if cards.has_next %}
        <a href="{{ url_for('admin.cards', page=cards.next_num, status=status) }}">下一页 ›</a>
    {% endif %}
</div>
{% endif %}

{% else %}
<div class="card" style="text-align: center; padding: 3rem;">
    <h3 style="color: #666; margin-bottom: 1rem;">暂无卡密</h3>
    <p style="color: #999; margin-bottom: 2rem;">
        {% if status == 'used' %}
            暂无已使用的卡密
        {% elif status == 'unused' %}
            暂无未使用的卡密
        {% else %}
            系统中还没有任何卡密
        {% endif %}
    </p>
    <a href="{{ url_for('admin.create_cards') }}" class="btn btn-primary">创建卡密</a>
</div>
{% endif %}

<div style="text-align: center; margin-top: 2rem;">
    <a href="{{ url_for('admin.create_cards') }}" class="btn btn-success">创建新卡密</a>
    <a href="{{ url_for('admin.dashboard') }}" class="btn btn-secondary">返回管理后台</a>
</div>
{% endblock %}
