#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
验证API文档和实际实现的一致性
检查API接口、参数、响应格式等是否匹配
"""

import sys
import os
import requests
import json
import time
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_api_status():
    """测试API状态接口"""
    print("🔍 测试API状态接口...")
    
    try:
        url = "http://localhost:5000/api/status"
        response = requests.get(url, timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success') and 'data' in data:
                print("   ✅ API状态接口正常")
                print(f"   📋 版本: {data['data'].get('version')}")
                print(f"   📋 模式: {data['data'].get('mode')}")
                return True
            else:
                print("   ❌ API状态接口响应格式错误")
                return False
        else:
            print(f"   ❌ API状态接口返回错误状态码: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ API状态接口测试失败: {e}")
        return False

def test_login_api():
    """测试登录API接口"""
    print("🔍 测试登录API接口...")
    
    try:
        url = "http://localhost:5000/api/login"
        
        # 测试正确登录
        data = {"username": "admin", "password": "admin123"}
        response = requests.post(url, json=data, timeout=5)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success') and 'data' in result:
                token = result['data'].get('token')
                if token:
                    print("   ✅ 登录API接口正常")
                    print(f"   📋 用户: {result['data'].get('username')}")
                    print(f"   📋 角色: {result['data'].get('role')}")
                    print(f"   📋 令牌长度: {len(token)}")
                    return token
                else:
                    print("   ❌ 登录响应中缺少令牌")
                    return None
            else:
                print("   ❌ 登录API响应格式错误")
                return None
        else:
            print(f"   ❌ 登录API返回错误状态码: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"   ❌ 登录API测试失败: {e}")
        return None

def test_user_info_api():
    """测试用户信息API接口"""
    print("🔍 测试用户信息API接口...")
    
    try:
        # 测试GET方式
        url = "http://localhost:5000/api/user_info"
        params = {"username": "admin"}
        response = requests.get(url, params=params, timeout=5)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success') and 'data' in result:
                print("   ✅ 用户信息API (GET) 正常")
                print(f"   📋 用户ID: {result['data'].get('user_id')}")
                print(f"   📋 用户名: {result['data'].get('username')}")
                print(f"   📋 角色: {result['data'].get('role')}")
                
                # 测试POST方式
                data = {"username": "admin"}
                response = requests.post(url, json=data, timeout=5)
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get('success'):
                        print("   ✅ 用户信息API (POST) 正常")
                        return True
                    else:
                        print("   ❌ 用户信息API (POST) 响应格式错误")
                        return False
                else:
                    print(f"   ❌ 用户信息API (POST) 返回错误状态码: {response.status_code}")
                    return False
            else:
                print("   ❌ 用户信息API (GET) 响应格式错误")
                return False
        else:
            print(f"   ❌ 用户信息API (GET) 返回错误状态码: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ 用户信息API测试失败: {e}")
        return False

def test_token_verification_api(token):
    """测试令牌验证API接口"""
    print("🔍 测试令牌验证API接口...")
    
    if not token:
        print("   ⚠️  跳过令牌验证测试（无有效令牌）")
        return False
    
    try:
        url = "http://localhost:5000/api/verify_token"
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.post(url, headers=headers, timeout=5)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("   ✅ 令牌验证API正常")
                print(f"   📋 令牌有效: {result['data'].get('valid', True)}")
                return True
            else:
                print("   ❌ 令牌验证API响应格式错误")
                return False
        else:
            print(f"   ❌ 令牌验证API返回错误状态码: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ 令牌验证API测试失败: {e}")
        return False

def test_api_key_endpoints():
    """测试需要API密钥的接口"""
    print("🔍 测试API密钥接口...")
    
    # 这里需要一个有效的API密钥，通常从数据库获取
    # 为了测试，我们先检查是否有软件记录
    try:
        # 测试无API密钥的情况
        url = "http://localhost:5000/api/check_user"
        data = {"username": "admin"}
        response = requests.post(url, json=data, timeout=5)
        
        if response.status_code == 401:
            result = response.json()
            if result.get('error_code') == 'MISSING_API_KEY':
                print("   ✅ API密钥验证正常（正确拒绝无密钥请求）")
                return True
            else:
                print("   ❌ API密钥验证错误码不匹配")
                return False
        else:
            print(f"   ❌ API密钥验证返回意外状态码: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ API密钥接口测试失败: {e}")
        return False

def test_error_responses():
    """测试错误响应格式"""
    print("🔍 测试错误响应格式...")
    
    try:
        # 测试错误的登录凭据
        url = "http://localhost:5000/api/login"
        data = {"username": "nonexistent", "password": "wrongpassword"}
        response = requests.post(url, json=data, timeout=5)
        
        if response.status_code == 401:
            result = response.json()
            if (result.get('success') == False and 
                'error' in result and 
                'error_code' in result):
                print("   ✅ 错误响应格式正确")
                print(f"   📋 错误码: {result.get('error_code')}")
                print(f"   📋 错误信息: {result.get('error')}")
                return True
            else:
                print("   ❌ 错误响应格式不正确")
                print(f"   📋 实际响应: {result}")
                return False
        else:
            print(f"   ❌ 错误响应状态码不正确: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ 错误响应测试失败: {e}")
        return False

def check_api_documentation():
    """检查API文档是否存在"""
    print("🔍 检查API文档...")
    
    doc_files = [
        "docs/API_REFERENCE.md",
        "docs/API_LOGIN_GUIDE.md"
    ]
    
    all_exist = True
    for doc_file in doc_files:
        if os.path.exists(doc_file):
            print(f"   ✅ {doc_file} 存在")
        else:
            print(f"   ❌ {doc_file} 不存在")
            all_exist = False
    
    return all_exist

def main():
    """主函数"""
    print("🚀 开始验证API文档和实现的一致性")
    print("=" * 60)
    
    results = []
    
    # 1. 检查API文档
    results.append(("API文档检查", check_api_documentation()))
    
    # 2. 测试API状态
    results.append(("API状态接口", test_api_status()))
    
    # 3. 测试登录API
    token = test_login_api()
    results.append(("登录API接口", token is not None))
    
    # 4. 测试用户信息API
    results.append(("用户信息API接口", test_user_info_api()))
    
    # 5. 测试令牌验证API
    results.append(("令牌验证API接口", test_token_verification_api(token)))
    
    # 6. 测试API密钥接口
    results.append(("API密钥接口", test_api_key_endpoints()))
    
    # 7. 测试错误响应
    results.append(("错误响应格式", test_error_responses()))
    
    # 输出测试结果
    print("\n📊 测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！API文档和实现一致性良好。")
        return True
    else:
        print("⚠️  部分测试失败，请检查API实现或文档。")
        return False

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n✅ 验证完成！")
            sys.exit(0)
        else:
            print("\n❌ 验证失败！")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⚠️  验证被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 验证过程中发生错误: {e}")
        sys.exit(1)
