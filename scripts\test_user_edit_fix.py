#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试用户编辑功能修复
验证 updated_at 字段问题是否已解决
"""

import sys
import os
import sqlite3
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def check_user_table_structure():
    """检查用户表结构"""
    print("🔍 检查用户表结构...")
    
    try:
        conn = sqlite3.connect('point_card_platform.db')
        cursor = conn.cursor()
        
        # 检查表结构
        cursor.execute("PRAGMA table_info(user)")
        columns = cursor.fetchall()
        
        print("   📋 用户表结构:")
        column_names = []
        for col in columns:
            column_names.append(col[1])
            print(f"      - {col[1]}: {col[2]} (NOT NULL: {col[3]})")
        
        # 检查是否有 updated_at 字段
        has_updated_at = 'updated_at' in column_names
        print(f"   📋 是否有 updated_at 字段: {has_updated_at}")
        
        conn.close()
        return True, has_updated_at
        
    except Exception as e:
        print(f"   ❌ 检查用户表结构失败: {e}")
        return False, False

def test_update_user_info():
    """测试用户信息更新功能"""
    print("👤 测试用户信息更新功能...")
    
    try:
        from bypass_sqlalchemy import DatabaseManager
        
        db_manager = DatabaseManager()
        
        # 创建测试用户
        test_username = "testuser456"
        test_email = "<EMAIL>"
        test_password = "testpass456"
        
        # 检查测试用户是否已存在
        existing_user = db_manager.get_user_by_username(test_username)
        if existing_user:
            user_id = existing_user['id']
            print(f"   📋 使用现有测试用户: {test_username} (ID: {user_id})")
        else:
            # 创建测试用户
            password_hash = db_manager.hash_password(test_password)
            
            with db_manager.get_connection() as conn:
                cursor = conn.cursor()
                current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                cursor.execute("""
                    INSERT INTO user (username, email, password_hash, is_admin, is_super_admin, points, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (test_username, test_email, password_hash, False, False, 100, current_time))
                conn.commit()
                user_id = cursor.lastrowid
            
            print(f"   📋 创建测试用户: {test_username} (ID: {user_id})")
        
        # 测试更新用户信息
        new_username = "updateduser456"
        new_email = "<EMAIL>"
        new_password = "newpassword456"
        new_password_hash = db_manager.hash_password(new_password)
        
        print(f"   🔧 测试更新用户信息...")
        print(f"      原用户名: {test_username} -> {new_username}")
        print(f"      原邮箱: {test_email} -> {new_email}")
        print(f"      密码: 已更新")
        
        # 执行更新
        success = db_manager.update_user_info(
            user_id=user_id,
            username=new_username,
            email=new_email,
            password_hash=new_password_hash
        )
        
        if success:
            print(f"   ✅ 用户信息更新成功")
            
            # 验证更新结果
            updated_user = db_manager.get_user_by_id(user_id)
            if (updated_user and 
                updated_user['username'] == new_username and 
                updated_user['email'] == new_email):
                print(f"   ✅ 更新验证成功")
                print(f"      新用户名: {updated_user['username']}")
                print(f"      新邮箱: {updated_user['email']}")
                
                # 验证密码更新
                if db_manager.verify_password(new_password, updated_user['password_hash']):
                    print(f"   ✅ 密码更新验证成功")
                else:
                    print(f"   ❌ 密码更新验证失败")
                    return False
                
                # 清理测试用户
                with db_manager.get_connection() as conn:
                    cursor = conn.cursor()
                    cursor.execute("DELETE FROM user WHERE id = ?", (user_id,))
                    conn.commit()
                print(f"   🗑️  测试用户已清理")
                
                return True
            else:
                print(f"   ❌ 更新验证失败")
                return False
        else:
            print(f"   ❌ 用户信息更新失败")
            return False
            
    except Exception as e:
        print(f"   ❌ 测试用户信息更新失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_user_edit_route_simulation():
    """模拟用户编辑路由的完整流程"""
    print("🌐 模拟用户编辑路由流程...")
    
    try:
        from bypass_sqlalchemy import DatabaseManager
        from routes.auth import validate_username, validate_email
        
        db_manager = DatabaseManager()
        
        # 模拟表单数据
        form_data = {
            'username': 'testuser789',
            'email': '<EMAIL>',
            'password': 'newpass123',
            'confirm_password': 'newpass123'
        }
        
        print(f"   📋 模拟表单数据:")
        print(f"      用户名: {form_data['username']}")
        print(f"      邮箱: {form_data['email']}")
        print(f"      密码: {'*' * len(form_data['password'])}")
        
        # 1. 验证用户名格式
        is_valid_username, username_msg = validate_username(form_data['username'])
        if not is_valid_username:
            print(f"   ❌ 用户名验证失败: {username_msg}")
            return False
        print(f"   ✅ 用户名格式验证通过")
        
        # 2. 验证邮箱格式
        if not validate_email(form_data['email']):
            print(f"   ❌ 邮箱格式验证失败")
            return False
        print(f"   ✅ 邮箱格式验证通过")
        
        # 3. 验证密码
        if len(form_data['password']) < 6:
            print(f"   ❌ 密码长度不足")
            return False
        
        if form_data['password'] != form_data['confirm_password']:
            print(f"   ❌ 密码确认不匹配")
            return False
        print(f"   ✅ 密码验证通过")
        
        # 4. 检查用户名和邮箱唯一性（模拟）
        existing_user = db_manager.get_user_by_username(form_data['username'])
        if existing_user:
            print(f"   ⚠️  用户名已存在（这在实际编辑中会检查是否是同一用户）")
        
        existing_email_user = db_manager.get_user_by_email(form_data['email'])
        if existing_email_user:
            print(f"   ⚠️  邮箱已存在（这在实际编辑中会检查是否是同一用户）")
        
        print(f"   ✅ 唯一性检查通过")
        
        # 5. 生成密码哈希
        password_hash = db_manager.hash_password(form_data['password'])
        print(f"   ✅ 密码哈希生成成功")
        
        print(f"   ✅ 用户编辑路由流程模拟成功")
        return True
        
    except Exception as e:
        print(f"   ❌ 模拟用户编辑路由流程失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_usage_instructions():
    """显示使用说明"""
    print("\n📖 用户编辑功能使用说明")
    print("=" * 60)
    print("🔧 修复内容:")
    print("   - 修复了 update_user_info 方法中的 updated_at 字段问题")
    print("   - 方法现在会动态检查表结构，兼容有无 updated_at 字段的情况")
    print()
    print("👑 超级管理员编辑用户:")
    print("   1. 使用超级管理员账号登录 (superadmin / super123)")
    print("   2. 访问 /admin/users 页面")
    print("   3. 点击用户列表中的 '编辑' 按钮")
    print("   4. 修改用户名、邮箱或密码")
    print("   5. 点击 '保存修改' 按钮")
    print()
    print("⚠️  注意事项:")
    print("   - 用户名只能包含英文字母和数字，必须以字母开头")
    print("   - 邮箱必须是有效的邮箱格式")
    print("   - 密码长度至少6位")
    print("   - 修改后用户需要使用新凭据重新登录")

def main():
    """主函数"""
    print("🚀 开始测试用户编辑功能修复")
    print("=" * 60)
    
    results = []
    
    # 1. 检查用户表结构
    table_ok, has_updated_at = check_user_table_structure()
    results.append(("用户表结构检查", table_ok))
    
    if table_ok:
        if has_updated_at:
            print("   ℹ️  用户表有 updated_at 字段")
        else:
            print("   ℹ️  用户表没有 updated_at 字段（这是正常的）")
    
    # 2. 测试用户信息更新
    results.append(("用户信息更新", test_update_user_info()))
    
    # 3. 模拟用户编辑路由流程
    results.append(("用户编辑路由流程", test_user_edit_route_simulation()))
    
    # 输出结果
    print("\n📊 测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 成功" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 用户编辑功能修复验证通过！")
        show_usage_instructions()
        return True
    else:
        print("⚠️  部分测试失败，可能还有问题需要解决。")
        return False

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n✅ 用户编辑功能现在应该可以正常工作！")
            sys.exit(0)
        else:
            print("\n❌ 用户编辑功能测试失败！")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⚠️  测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
